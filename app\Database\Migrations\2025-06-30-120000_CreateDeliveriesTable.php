<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateDeliveriesTable extends Migration
{
    public function up()
    {
        // Define table structure
        $this->forge->addField([
            'id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'auto_increment' => true,
            ],
            'company_id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
            ],
            'supplier_id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
            ],
            'material_id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
            ],
            'warehouse_id' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
            ],
            'delivery_date' => [
                'type'           => 'DATE',
            ],
            'reference_number' => [
                'type'           => 'VARCHAR',
                'constraint'     => 100,
            ],
            'quantity' => [
                'type'           => 'DECIMAL',
                'constraint'     => '15,2',
                'default'        => 0.00,
            ],
            'unit_price' => [
                'type'           => 'DECIMAL',
                'constraint'     => '15,2',
                'default'        => 0.00,
            ],
            'total_amount' => [
                'type'           => 'DECIMAL',
                'constraint'     => '15,2',
                'default'        => 0.00,
            ],
            'status' => [
                'type'           => 'VARCHAR',
                'constraint'     => 50,
                'default'        => 'pending',
                'comment'        => 'pending, received, cancelled',
            ],
            'notes' => [
                'type'           => 'TEXT',
                'null'           => true,
            ],
            'created_by' => [
                'type'           => 'INT',
                'constraint'     => 11,
                'unsigned'       => true,
                'null'           => false,
            ],
            'created_at' => [
                'type'           => 'DATETIME',
                'null'           => true,
            ],
            'updated_at' => [
                'type'           => 'DATETIME',
                'null'           => true,
            ],
        ]);

        // Define primary key
        $this->forge->addKey('id', true);
        
        // Define foreign keys
        $this->forge->addForeignKey('company_id', 'companies', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('supplier_id', 'suppliers', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('material_id', 'materials', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('warehouse_id', 'warehouses', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('created_by', 'users', 'id', 'CASCADE', 'CASCADE');
        
        // Create the table
        $this->forge->createTable('deliveries');
    }

    public function down()
    {
        // Drop the table
        $this->forge->dropTable('deliveries');
    }
}
