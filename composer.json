{"name": "codeigniter4/appstarter", "description": "CodeIgniter4 starter app", "license": "MIT", "type": "project", "homepage": "https://codeigniter.com", "support": {"forum": "https://forum.codeigniter.com/", "source": "https://github.com/codeigniter4/CodeIgniter4", "slack": "https://codeigniterchat.slack.com"}, "require": {"php": "^7.4 || ^8.0", "codeigniter4/framework": "^4.0", "dompdf/dompdf": "^2.0", "mpdf/mpdf": "^8.2", "phpoffice/phpspreadsheet": "^1.29", "phpoffice/phpword": "^1.2"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": "^9.1"}, "autoload": {"exclude-from-classmap": ["**/Database/Migrations/**"]}, "autoload-dev": {"psr-4": {"Tests\\Support\\": "tests/_support"}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "scripts": {"test": "phpunit"}}