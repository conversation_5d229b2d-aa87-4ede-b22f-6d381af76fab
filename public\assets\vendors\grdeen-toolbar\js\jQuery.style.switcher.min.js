(function(e){var t,n={hasPreview:true,defaultThemeId:"jssDefault",fullPath:"css/",cookie:{expires:30,isManagingLoad:true}},r="jss_selected",i={};i={getItem:function(e){if(!e){return null}return decodeURIComponent(document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1"))||null},setItem:function(e,t,n,r,i,s){if(!e||/^(?:expires|max\-age|path|domain|secure)$/i.test(e)){return false}var o="";if(n){switch(n.constructor){case Number:o=n===Infinity?"; expires=Fri, 31 Dec 9999 23:59:59 GMT":"; max-age="+n;break;case String:o="; expires="+n;break;case Date:o="; expires="+n.toUTCString();break}}document.cookie=encodeURIComponent(e)+"="+encodeURIComponent(t)+o+(i?"; domain="+i:"")+(r?"; path="+r:"")+(s?"; secure":"");return true},removeItem:function(e,t,n){if(!this.hasItem(e)){return false}document.cookie=encodeURIComponent(e)+"=; expires=Thu, 01 Jan 1970 00:00:00 GMT"+(n?"; domain="+n:"")+(t?"; path="+t:"");return true},hasItem:function(e){if(!e){return false}return(new RegExp("(?:^|;\\s*)"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,"\\$&")+"\\s*\\=")).test(document.cookie)},keys:function(){var e=document.cookie.replace(/((?:^|\s*;)[^\=]+)(?=;|$)|^\s*|\s*(?:\=[^;]*)?(?:\1|$)/g,"").split(/\s*(?:\=[^;]*)?;\s*/);for(var t=e.length,n=0;n<t;n++){e[n]=decodeURIComponent(e[n])}return e}};t=function(e,t){return this.init(e,t)};t.prototype={$root:null,config:{},$themeCss:null,defaultTheme:null,init:function(e,t){this.$root=e;this.config=t?t:{};this.setDefaultTheme();if(this.defaultTheme){if(this.config.cookie){this.checkCookie()}if(this.config.hasPreview){this.addHoverEvents()}this.addClickEvents()}else{this.$root.addClass("jssError error level0")}},setDefaultTheme:function(){this.$themeCss=e("link[id="+this.config.defaultThemeId+"]");if(this.$themeCss.length){this.defaultTheme=this.$themeCss.attr("href")}},resetStyle:function(){this.updateStyle(this.defaultTheme)},updateStyle:function(e){this.$themeCss.attr("href",e)},getFullAssetPath:function(e){return this.config.fullPath+e+".css"},checkCookie:function(){var e;if(this.config.cookie&&this.config.cookie.isManagingLoad){e=i.getItem(r);if(e){newStyle=this.getFullAssetPath(e);this.updateStyle(newStyle);this.defaultTheme=newStyle}}},addHoverEvents:function(){var t=this;this.$root.find("a").hover(function(){var n=e(this).data("theme"),r=t.getFullAssetPath(n);t.updateStyle(r)},function(){t.resetStyle()})},addClickEvents:function(){var t=this;this.$root.find("a").click(function(){var n=e(this).data("theme"),s=t.getFullAssetPath(n);t.updateStyle(s);t.defaultTheme=s;if(t.config.cookie){i.setItem(r,n,t.config.cookie.expires)}})}};e.fn.styleSwitcher=function(r){return new t(this,e.extend(true,n,r))}})(jQuery)