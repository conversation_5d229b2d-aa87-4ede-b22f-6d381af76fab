Proposal for Construction Management
System (infocus Technologies )
Submitted by:
Misheck Kamuloni
Software Developer & IT Project Manager
Date: 21 June 2025
1. Introduction
   This proposal outlines the development of a robust Construction Management System
   (CMS) tailored for construction companies to manage operations more efficiently. The
   system will include core modules such as Project Management, Inventory, Accounting, HR
   with Payroll, User Management, and File Management. Optional advanced modules and a
   mobile app are also available.
2. Objectives
- Centralize digital operations across departments
- Improve collaboration and monitoring
- Automate HR, finance, and inventory workflows
- Enable real-time mobile access for on-site updates
- Ensure accountability and operational transparency
3. Core System Modules and Features
   Project Management
   • - Create and manage multiple projects and tasks
   • - Assign team members and define roles
   • - Track milestones and task statuses
   • - Visual timeline using Gantt charts
   • - Attach documents to tasks
   • - Progress tracking and activity logs
   • - Email reminders for deadlines and overdue tasks
   • - Budget tracking and per-project dashboards
   Inventory Management
   • - Manage stock levels across sites
   • - Log stock in/out with user traceability
   • - Material usage reporting by project
   • - Low stock alert notifications
   • - Supplier and delivery record management
   • - Barcode entry support (optional)
   Accounting & Financial
   • - Budgeting and financial planning per project
   • - Record income and expenditures
   • - Profit/Loss and cash flow reporting
   • - Manual invoice and receipt management
   • - Tax and deduction tracking
   • - Export reports to PDF or Excel
   HR & Payroll
   • - Employee profiles and role management
   • - Attendance tracking (daily/hourly)
   • - Leave management and approval workflow
   • - Payroll generation with allowances and deductions
   • - Payslip creation and export
   • - Historical salary records
   User Management & Access Control
   • - Create user roles and permissions
   • - Restrict access by module/function
   • - Audit log of user actions
   • - Two-factor authentication support
   File Management
   • - Upload and organize files by project/type
   • - Document preview and version history
   • - Access controls for view/edit/delete
   • - Search and tagging support
   • - Commenting on files and change tracking
   Equipment & Asset Tracking
   • - Register and assign tools and machines
   • - Track equipment by site or user
   • - Schedule and log maintenance
   • - Damage and downtime reports
   • - Condition status monitoring
   Timesheet Management
   • - Time logging by employee/project
   • - Supervisor approvals and comments
   • - Integration with payroll
   • - Notification for missing entries
   • - Timesheet exports for audits
   Incident & Safety Reporting
   • - Log incidents with photos and notes
   • - Track resolution and action steps
   • - Upload safety audits and reports
   • - Generate safety trend analytics
   • - Filter incidents by type/severity/project
4. Optional Advanced Modules (Phase 2)
   Mobile App
   • - View and update tasks from site
   • - Upload progress photos
   • - Clock in/out with GPS
   • - Request materials on-site
   • - Push notifications for tasks and approvals
   • - View documents and plans
5. Implementation Timeline & Costing
   The core system will be delivered over a 45-day period, broken into the following phases:
- Requirements & Design: 5 days
- Development: 30 days
- Testing & Feedback: 5 days
- Deployment & Training: 5 days
  • **Core System Development: MWK 3,500,000**
- Initial Payment (Start): MWK 1,200,000
- Mid-Project Payment: MWK 1,200,000
- Final Payment (Completion): MWK 1,100,000
  • **Optional Costs:**
- Mobile App: MWK 1,200,000
  **Annual Hosting Fee:** MWK 450,000
  *Client may use their own hosting if preferred.*
6. Conclusion
   This system provides a comprehensive platform for managing construction operations end￾to-end, from people and projects to payroll and assets. With optional mobile access and
   extendable modules, the solution is scalable, efficient, and ready for real-world field use.