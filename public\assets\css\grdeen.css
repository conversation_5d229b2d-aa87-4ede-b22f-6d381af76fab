/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Utility
# Cards
# Common
# Form
# Navigations
# Animations
# Mobile Nav
# Search Popup
# Page Header
# Google Map
# Client Carousel
--------------------------------------------------------------*/
:root {
  --grdeen-font: "Inter", sans-serif;
  --grdeen-heading-font: "DM Sans", sans-serif;
  --grdeen-special-font: "<PERSON> Brush", cursive;
  --grdeen-text: #0d703a;
  --grdeen-text-rgb: 98, 111, 98;
  --grdeen-text-dark: #0d703a;
  --grdeen-text-dark-rgb: 7, 55, 10;
  --grdeen-text-dark2: #0e150e;
  --grdeen-text-dark2-rgb: 14, 21, 14;
  --grdeen-base: #0d703a;
  --grdeen-base-rgb: 26, 145, 32;
  --grdeen-green: #0d703a;
  --grdeen-green-rgb: #0d703a;
  --grdeen-gray: #f6f7f2;
  --grdeen-gray-rgb: 246, 247, 242;
  --grdeen-gray2: #f1f4f1;
  --grdeen-gray2-rgb: 241, 244, 241;
  --grdeen-white: #fff;
  --grdeen-white-rgb: 255, 255, 255;
  --grdeen-white2: #f2f4ec;
  --grdeen-white2-rgb: 242, 244, 236;
  --grdeen-white3: #faf8ec;
  --grdeen-white3-rgb: 250, 248, 236;
  --grdeen-white4: #ecf2df;
  --grdeen-white4-rgb: 236, 242, 223;
  --grdeen-black: #172000;
  --grdeen-black-rgb: 23, 32, 0;
  --grdeen-black2: #4f5345;
  --grdeen-black2-rgb: 79, 83, 69;
  --grdeen-black3: #545454;
  --grdeen-black3-rgb: 84, 84, 84;
  --grdeen-black4: #0d190e;
  --grdeen-black4-rgb: 13, 25, 14;
  --grdeen-border-color: #e7e7e7;
  --grdeen-border-color-rgb: 231, 231, 231;
}

/*--------------------------------------------------------------
# Utility
--------------------------------------------------------------*/
.mt-20 {
  margin-top: 20px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-50 {
  margin-top: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-80 {
  margin-top: 80px;
}

.mt-120 {
  margin-top: 120px;
}

.mt--60 {
  margin-top: -60px;
}

.mt--120 {
  margin-top: -120px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-80 {
  margin-bottom: 80px;
}

.mb-120 {
  margin-bottom: 120px;
}

.mb--60 {
  margin-bottom: -60px;
}

.mb--120 {
  margin-bottom: -120px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-50 {
  padding-top: 50px;
}

.pt-60 {
  padding-top: 60px;
}

.pt-80 {
  padding-top: 80px;
}

.pt-100 {
  padding-top: 100px;
}

.pt-110 {
  padding-top: 110px;
}

.pt-115 {
  padding-top: 115px;
}

.pt-120 {
  padding-top: 120px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-30 {
  padding-left: 30px;
}

.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-30 {
  padding-right: 30px;
}

/*--------------------------------------------------------------
# Common
--------------------------------------------------------------*/
body {
  font-family: var(--grdeen-font, "Inter", sans-serif);
  color: var(--grdeen-text, #626f62);
  font-size: 16px;
  font-weight: 400;
}

body.locked {
  overflow: hidden;
}

a {
  color: var(--grdeen-base, #1a9120);
  transition: all 400ms ease;
}

a,
a:hover,
a:focus,
a:visited {
  text-decoration: none;
}

::placeholder {
  color: inherit;
  opacity: 1;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  color: var(--grdeen-text-dark, #07370a);
}
@media (max-width: 575px) {
  h1 br,
  h2 br,
  h3 br,
  h4 br,
  h5 br,
  h6 br {
    display: none;
  }
}

@media (max-width: 575px) {
  p br {
    display: none;
  }
}

::placeholder {
  color: inherit;
  opacity: 1;
}

.background-base {
  background-color: var(--grdeen-base, #0d703a);
}

.background-gray {
  background-color: var(--grdeen-gray, #f6f7f2);
}

.background-black {
  background-color: var(--grdeen-black, #172000);
}

.background-black-2 {
  background-color: var(--grdeen-black2, #4f5345);
}

.grdeen-text-dark {
  color: var(--grdeen-text-dark, #07370a);
}

.page-wrapper {
  position: relative;
  margin: 0 auto;
  width: 100%;
  min-width: 300px;
  overflow: hidden;
}

.container-fluid,
.container {
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 1200px) {
  .container {
    max-width: 1200px;
  }
}
.row {
  --bs-gutter-x: 30px;
}

.gutter-y-10 {
  --bs-gutter-y: 10px;
}

.gutter-y-15 {
  --bs-gutter-y: 15px;
}

.gutter-y-20 {
  --bs-gutter-y: 20px;
}

.gutter-y-30 {
  --bs-gutter-y: 30px;
}

.gutter-y-60 {
  --bs-gutter-y: 60px;
}

.grdeen-btn {
  display: inline-block;
  vertical-align: middle;
  -webkit-appearance: none;
  border: none;
  outline: none !important;
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  font-size: 13px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  padding: 16.25px 48px;
  border-radius: 4px;
  transition: 500ms;
  text-transform: uppercase;
  position: relative;
  overflow: hidden;
  text-align: center;
  transition: all 0.7s ease;
}
.grdeen-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -16px;
  right: 0;
  bottom: 0;
  width: 130%;
  height: 0%;
  margin: auto;
  transform: rotate(-45deg);
  background-color: var(--grdeen-black, #172000);
  transition: 700ms cubic-bezier(0.6, 1.5, 0.4, 0.7);
}
.grdeen-btn:hover {
  color: var(--grdeen-white, #fff);
}
.grdeen-btn:hover::before {
  height: 450%;
}
.grdeen-btn span {
  position: relative;
  color: inherit;
  z-index: 2;
}
.grdeen-btn--black:hover {
  color: var(--grdeen-white, #fff);
}
.grdeen-btn--black::after {
  background-color: var(--grdeen-base, #1a9120);
}
.grdeen-btn--black::before {
  background-color: var(--grdeen-black, #172000);
}
.grdeen-btn--base:hover {
  color: var(--grdeen-white, #fff);
}
.grdeen-btn--base::after {
  background-color: var(--grdeen-black, #172000);
}
.grdeen-btn--base::before {
  background-color: var(--grdeen-base, #1a9120);
}

.tabs-box .tabs-content .tab:not(.active-tab) {
  display: none;
}

.bootstrap-select .dropdown-menu {
  padding-top: 0;
  padding-bottom: 0;
  border-radius: 0;
}
.bootstrap-select .dropdown-menu li a {
  padding: 10px 20px;
}
.bootstrap-select .dropdown-item.active,
.bootstrap-select .dropdown-item:hover {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
}

.tns-outer .tns-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
}
.tns-outer .tns-controls button {
  width: 45px;
  height: 45px;
  border: 2px solid #f4f4f4;
  outline: none;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--grdeen-text, #626f62);
  border-radius: 50%;
  margin-left: 5px;
  margin-right: 5px;
}

.block-title {
  margin-top: -8px;
  margin-bottom: 50px;
}
.block-title__decor {
  width: 21px;
  height: 14px;
  background-image: url(../images/shapes/leaf-1-1.html);
  background-repeat: no-repeat;
  background-position: top center;
  display: inline-block;
  line-height: 1;
  margin-bottom: -5px;
  position: relative;
  top: -7px;
}
.block-title p {
  margin: 0;
  color: var(--grdeen-text, #626f62);
  font-size: 16px;
  line-height: 1;
  margin-bottom: 7px;
}
@media (min-width: 768px) {
  .block-title p {
    font-size: 18px;
  }
}
@media (min-width: 992px) {
  .block-title p {
    font-size: 20px;
  }
}
.block-title h3 {
  margin: 0;
  font-size: 35px;
  color: var(--grdeen-black, #172000);
  font-family: var(--grdeen-special-font, "Alex Brush", cursive);
}
@media (min-width: 768px) {
  .block-title h3 {
    font-size: 42px;
  }
}
@media (min-width: 992px) {
  .block-title h3 {
    font-size: 50px;
  }
}

.ul-list-one {
  margin-bottom: 0;
}
.ul-list-one li {
  position: relative;
  padding-left: 45px;
  font-size: 16px;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
}
@media (min-width: 481px) {
  .ul-list-one li {
    font-size: 90px;
  }
}
.ul-list-one li::before {
  content: "\e907";
  color: var(--grdeen-base, #1a9120);
  font-size: 26px;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  font-family: "azino-icon";
}

.preloader {
  position: fixed;
  background-color: var(--grdeen-black, #172000);
  background-position: center center;
  background-repeat: no-repeat;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9991;
  display: -webkit-box;
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  text-align: center;
}
.preloader__image {
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation-name: flipInY;
  animation-name: flipInY;
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 60px auto;
  width: 100%;
  height: 100%;
}

/* scroll to top */
.scroll-to-top {
  display: flex;
  align-items: center;
  width: auto;
  height: 35px;
  background: transparent;
  position: fixed;
  bottom: 60px;
  right: -12px;
  z-index: 99;
  text-align: center;
  opacity: 0;
  visibility: hidden;
  transform: rotate(-90deg);
  cursor: pointer;
  transition: all 0.2s ease;
}
.scroll-to-top__text {
  display: inline;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  font-weight: 700;
  margin-right: 8px;
}
.scroll-to-top__wrapper {
  display: inline-block;
  width: 30px;
  height: 4px;
  background-color: var(--grdeen-base, #1a9120);
  position: relative;
  overflow: hidden;
}
.scroll-to-top__inner {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--grdeen-black, #172000);
}
.scroll-to-top.show {
  opacity: 1;
  visibility: visible;
  bottom: 70px;
}

/* post paginations */
.post-pagination {
  margin-bottom: 0;
  margin-top: 0px;
}
@media (min-width: 992px) {
  .post-pagination {
    margin-top: 0px;
  }
}
.post-pagination a {
  display: flex;
  width: 45px;
  height: 45px;
  background-color: #eff2f6;
  align-items: center;
  justify-content: center;
  color: var(--grdeen-text, #626f62);
  font-size: 16px;
  font-weight: 500;
  border-radius: 50%;
  transition: 500ms ease;
}
@media (min-width: 992px) {
  .post-pagination a {
    width: 60px;
    height: 60px;
    font-size: 18px;
  }
}
.post-pagination a:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: #fff;
}
.post-pagination li:first-child a {
  background-color: var(--grdeen-base, #1a9120);
  color: #fff;
}
.post-pagination li:last-child a {
  background-color: var(--grdeen-black, #172000);
  color: #fff;
}
.post-pagination li + li {
  margin-left: 10px;
}

.grdeen-owl__carousel--with-shadow .owl-stage-outer {
  overflow: visible;
}
.grdeen-owl__carousel--with-shadow .owl-item {
  opacity: 0;
  visibility: hidden;
  transition: opacity 500ms ease, visibility 500ms ease;
}
.grdeen-owl__carousel--with-shadow .owl-item.active {
  opacity: 1;
  visibility: visible;
}

.grdeen-owl__carousel--basic-nav.owl-carousel .owl-nav {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-nav button {
  border: none;
  outline: none;
  border-radius: 50%;
  margin: 0;
  padding: 0;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-nav button span {
  border: none;
  outline: none;
  width: 50px;
  height: 50px;
  background-color: var(--grdeen-gray, #f6f7f2);
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--grdeen-text, #626f62);
  border-radius: 50%;
  font-size: 14px;
  color: var(--grdeen-text, #626f62);
  transition: all 500ms ease;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-nav button span:hover {
  background-color: var(--grdeen-black, #172000);
  color: var(--grdeen-white, #fff);
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-nav.disabled {
  display: none !important;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0 8px;
  margin-top: 40px;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot {
  transition: all 0.5s ease;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot span {
  background-color: #edf0e8;
  width: 12px;
  height: 12px;
  margin: 0;
  display: block;
  border-radius: 30px;
  transition: all 0.5s ease;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot:hover span, .grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot.active span {
  background-color: var(--grdeen-white, #fff);
  border: 2px solid var(--grdeen-base, #1a9120);
  transition: all 0.5s ease;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots.disabled {
  display: none !important;
}
.grdeen-owl__carousel--basic-nav.owl-carousel .owl-nav.disabled + .owl-dots {
  margin-top: 40px;
}

.sec-title {
  padding-bottom: 38px;
}
@media (min-width: 768px) {
  .sec-title {
    padding-bottom: 55px;
  }
}
.sec-title__img {
  display: inline-flex;
  position: relative;
  color: var(--grdeen-base, #1a9120);
  font-size: 13px;
  line-height: 1;
  margin-right: 2px;
  top: 2px;
  animation: taglineEffect 1s linear infinite alternate;
}
.sec-title__img svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
@keyframes taglineEffect {
  from {
    transform: translateX(-2px);
  }
  to {
    transform: translateX(5px);
  }
}
.sec-title__tagline {
  margin: 0;
  color: var(--grdeen-base, #1a9120);
  font-weight: 600;
  font-size: 13px;
  line-height: 18px;
  text-transform: uppercase;
}
.sec-title__title {
  margin: 0;
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 600;
  font-size: 50px;
  line-height: 56px;
  margin-top: 8px;
}
@media (max-width: 1199px) {
  .sec-title__title {
    font-size: 45px;
    line-height: 52px;
  }
}
@media (max-width: 991px) {
  .sec-title__title {
    font-size: 42px;
    line-height: 44px;
  }
}
@media (max-width: 767px) {
  .sec-title__title {
    font-size: 32px;
    line-height: 34px;
  }
}

.ui-datepicker .ui-datepicker-header {
  background-image: none;
  background-color: var(--grdeen-black, #172000);
  color: var(--grdeen-white, #fff);
  font-family: var(--grdeen-font, "Inter", sans-serif);
}

.ui-datepicker-calendar th span {
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.ui-datepicker-calendar td {
  background-color: var(--grdeen-gray, #f6f7f2);
  background-image: none;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  color: var(--grdeen-text, #626f62);
}
.ui-datepicker-calendar td a {
  border-color: var(--grdeen-border-color, #e7e7e7);
  background-color: var(--grdeen-gray, #f6f7f2);
  background-image: none;
}
.ui-datepicker-calendar .ui-state-default,
.ui-datepicker-calendar .ui-widget-content .ui-state-default,
.ui-datepicker-calendar .ui-widget-header .ui-state-default {
  border-color: var(--grdeen-border-color, #e7e7e7);
  background-color: var(--grdeen-gray, #f6f7f2);
  background-image: none;
  color: var(--grdeen-text, #626f62);
  padding: 10px 5px;
  text-align: center;
  line-height: 1em;
}
.ui-datepicker-calendar .ui-state-default:hover,
.ui-datepicker-calendar .ui-widget-content .ui-state-default:hover,
.ui-datepicker-calendar .ui-widget-header .ui-state-default:hover {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
}
.ui-datepicker-calendar .ui-state-highlight,
.ui-datepicker-calendar .ui-widget-content .ui-state-highlight,
.ui-datepicker-calendar .ui-widget-header .ui-state-highlight {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
}

.ui-datepicker .ui-datepicker-prev,
.ui-datepicker .ui-datepicker-next {
  background-image: none;
  background-color: var(--grdeen-white, #fff);
  color: var(--grdeen-black, #172000);
}
.ui-datepicker .ui-datepicker-prev:hover,
.ui-datepicker .ui-datepicker-next:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  top: 2px;
}

.ui-datepicker .ui-datepicker-prev:hover {
  left: 2px;
}

.ui-datepicker .ui-datepicker-next:hover {
  right: 2px;
}

/*--------------------------------------------------------------
# Cards
--------------------------------------------------------------*/
.video-one {
  position: relative;
  background-color: var(--grdeen-black, #172000);
  padding: 100px 0;
}
.video-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--grdeen-black, #172000);
  background-size: cover;
  background-position: center center;
  opacity: 0.5;
}
.video-one .container {
  position: relative;
  text-align: center;
}
.video-one__btn {
  width: 145px;
  height: 145px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  position: relative;
}
.video-one__btn .video-popup {
  font-size: 24px;
  color: var(--grdeen-white, #fff);
  transition: all 500ms ease;
  position: relative;
  z-index: 10;
}
.video-one__btn .video-popup:hover {
  color: var(--grdeen-base, #1a9120);
}
.video-one__btn .curved-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 145px;
  height: 145px;
  transform-origin: center center;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: textRotate 15s linear 0s forwards infinite alternate;
}
.video-one__btn .curved-circle--item {
  width: 145px;
}
.video-one__btn .curved-circle--item span {
  text-transform: uppercase;
  font-size: 14px;
  color: var(--grdeen-white, #fff);
  letter-spacing: 0.4em;
}
.video-one__title {
  margin: 0;
  text-transform: uppercase;
  color: var(--grdeen-white, #fff);
  font-size: 40px;
  line-height: 1.2em;
  margin-bottom: 40px;
  margin-top: 30px;
}
@media (min-width: 768px) {
  .video-one__title {
    font-size: 50px;
  }
}
@media (min-width: 992px) {
  .video-one__title {
    font-size: 60px;
    margin-top: 20px;
    margin-bottom: 35px;
  }
}
.video-one__link::before {
  background-color: var(--grdeen-base, #1a9120);
}

.video-two {
  position: relative;
  background-color: var(--grdeen-black, #172000);
  padding: 143px 0 320px;
}
@media (max-width: 767px) {
  .video-two {
    padding: 100px 0 270px;
  }
  .video-two .text-end {
    text-align: left !important;
  }
}
.video-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--grdeen-black, #172000);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  opacity: 0.5;
}
.video-two__shape {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-position: left top;
  background-repeat: no-repeat;
  background-size: auto;
}
@media (max-width: 1199px) {
  .video-two__shape {
    display: none;
  }
}
.video-two .container {
  position: relative;
}
.video-two__btn {
  width: 145px;
  height: 145px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  position: relative;
  margin-top: 42px;
}
.video-two__btn .video-popup {
  font-size: 24px;
  color: var(--grdeen-white, #fff);
  transition: all 500ms ease;
  position: relative;
  z-index: 10;
}
.video-two__btn .video-popup:hover {
  color: var(--grdeen-base, #1a9120);
}
.video-two__btn .curved-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 145px;
  height: 145px;
  transform-origin: center center;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: textRotate 15s linear 0s forwards infinite alternate;
}
.video-two__btn .curved-circle--item {
  width: 145px !important;
  height: 145px !important;
}
.video-two__btn .curved-circle--item span {
  text-transform: uppercase;
  font-size: 14px;
  color: var(--grdeen-white, #fff);
  letter-spacing: 0.4em;
}
.video-two__title {
  margin: 0;
  text-transform: uppercase;
  color: var(--grdeen-white, #fff);
  font-size: 40px;
  line-height: 1.2em;
  margin-bottom: 40px;
}
@media (min-width: 768px) {
  .video-two__title {
    font-size: 50px;
  }
}
@media (min-width: 992px) {
  .video-two__title {
    font-size: 60px;
    margin-bottom: 35px;
  }
}
.video-two__link::before {
  background-color: var(--grdeen-base, #1a9120);
}

.team-one {
  padding-top: 165px;
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .team-one {
    padding-top: 120px;
    padding-bottom: 80px;
  }
}
.team-one--about {
  position: relative;
  padding-top: 112px;
}
@media (max-width: 767px) {
  .team-one--about {
    padding-top: 75px;
  }
}
.team-one--page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .team-one--page {
    padding: 80px 0;
  }
}
@media (max-width: 1200px) {
  .team-one .container {
    max-width: 100%;
  }
}
.team-one .sec-title {
  text-align: center;
  padding-bottom: 62px;
}
@media (max-width: 767px) {
  .team-one .sec-title {
    padding-bottom: 38px;
  }
}
.team-one .sec-title__title {
  margin-top: 0;
}
.team-one__item {
  position: relative;
  z-index: 1;
}
.team-one__item:hover .team-card__bg-black {
  background-color: var(--grdeen-base, #1a9120);
  height: 42%;
}
.team-one__item:hover .team-card__bg-base {
  background-color: var(--grdeen-black, #172000);
  height: 60%;
}
@media (min-width: 992px) {
  .team-one__carousel .owl-nav {
    display: none;
  }
}

.team-card {
  position: relative;
  z-index: 3;
  padding: 30px;
  padding-top: 38px;
  border-radius: 4px;
  overflow: hidden;
}
.team-card__bg-base, .team-card__bg-black {
  position: absolute;
  width: 100%;
  left: 0;
  transition: all 0.5s ease;
}
.team-card__bg-black {
  top: 0;
  height: 60%;
  background-color: var(--grdeen-black, #172000);
  border-radius: 4px;
  z-index: 2;
}
.team-card__bg-base {
  bottom: 0;
  height: 42%;
  background-color: var(--grdeen-base, #1a9120);
  z-index: 1;
  border-radius: 0 0 4px 4px;
}
.team-card__image {
  position: relative;
  width: 100%;
}
.team-card__image img {
  width: 100% !important;
  position: relative;
  max-width: 100%;
  border-radius: 8px !important;
}
.team-card__hover {
  position: relative;
}
.team-card__social {
  position: relative;
  cursor: pointer;
  transition: all 500ms ease;
}
.team-card__social__iconwrap {
  width: 53px;
  height: 53px;
  background-color: var(--grdeen-base, #1a9120);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-box-shadow: 0px 0px 0px 4px var(--grdeen-white, #fff);
  box-shadow: 0px 0px 0px 4px var(--grdeen-white, #fff);
  color: var(--grdeen-white, #fff);
  font-size: 17px;
  text-shadow: 0px 0px 1px var(--grdeen-white, #fff);
}
.team-card__social__iconwrap svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
.team-card__social__list {
  position: absolute;
  top: 0;
  left: 100%;
  transform: translate(0, 0%) rotate(90deg);
  background-color: var(--grdeen-black, #172000);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  min-height: 50px;
  padding-left: 20px;
  padding-right: 20px;
  opacity: 0;
  transition: 500ms ease;
  transform-origin: top left;
  border-radius: 5px;
}
.team-card__social__list a {
  color: var(--grdeen-white, #fff);
  font-size: 14px;
  transition: all 500ms ease;
  transform: rotate(-90deg);
}
.team-card__social__list a + a {
  margin-left: 27px;
}
.team-card__social__list a:hover {
  color: var(--grdeen-base, #1a9120);
}
.team-card__social:hover .team-card__social__list {
  opacity: 1;
  transform: translate(0, 105%) rotate(90deg);
}
.team-card__content {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
}
.team-card__title {
  margin: 0;
  font-size: 22px;
  line-height: 26px;
  color: var(--grdeen-white, #fff);
  font-weight: 700;
  margin-bottom: 4px;
  width: 100%;
}
.team-card__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.team-card__title a:hover {
  background-size: 100% 1px;
}
.team-card__title a:hover {
  color: var(--grdeen-white, #fff);
}
.team-card__designation {
  display: block;
  margin: 0;
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.5);
  font-size: 16px;
  line-height: 1;
}

.team-details {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .team-details {
    padding: 80px 0;
  }
}
.team-details__image {
  position: relative;
}
.team-details__image img {
  max-width: 100%;
  border-radius: 4px;
}
.team-details__content {
  position: relative;
}
@media (min-width: 1200px) {
  .team-details__content {
    margin: 0 43px 0 -53px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .team-details__content .col-md-6 {
    width: 100%;
  }
}
.team-details__title {
  margin: 0;
  font-weight: 600;
  font-size: 30px;
  line-height: 1;
  color: var(--grdeen-black, #172000);
  margin-top: -3px;
  margin-bottom: 3px;
}
.team-details__designation {
  margin: 0;
  font-size: 16px;
  color: var(--grdeen-base, #1a9120);
  margin-bottom: 27px;
}
.team-details__text {
  margin-bottom: 35px;
  color: var(--grdeen-black, #172000);
  line-height: 28px;
}
@media (min-width: 1200px) {
  .team-details__text {
    padding-right: 47px;
  }
}
.team-details__box {
  position: relative;
  background-color: var(--grdeen-gray2, #f1f4f1);
  padding: 27px 20px 22px 99px;
  border-radius: 7px;
}
.team-details__box__icon {
  width: 65px;
  height: 65px;
  background-color: var(--grdeen-base, #1a9120);
  border: 3px solid var(--grdeen-white, #fff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  margin: auto;
  font-size: 18px;
  color: var(--grdeen-white, #fff);
  transition: all 0.5s ease;
}
.team-details__box:hover .team-details__box__icon {
  background-color: var(--grdeen-black, #172000);
}
.team-details__box__title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 3px;
}
.team-details__box__text {
  margin: 0;
  font-size: 14px;
  line-height: 23px;
  font-weight: 500;
}
.team-details__box__text a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.team-details__box__text a:hover {
  background-size: 100% 1px;
}
.team-details__box__text a:hover {
  color: var(--grdeen-base, #1a9120);
}
.team-details__social {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 40px;
  margin-bottom: 40px;
}
.team-details__social__title {
  font-size: 20px;
  font-weight: 600;
  margin: 0 10px 0 0;
}
.team-details__social a {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--grdeen-gray2, #f1f4f1);
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  transition: all 500ms ease;
  border-radius: 50%;
}
.team-details__social a:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.team-details__info {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--grdeen-gray2, #f1f4f1);
  padding: 13px 30px;
  border-radius: 4px;
}
@media (max-width: 767px) {
  .team-details__info {
    display: block;
  }
}
.team-details__info__text {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: var(--grdeen-base, #1a9120);
}
@media (max-width: 767px) {
  .team-details__info__text {
    margin-bottom: 10px;
  }
}
.team-details__info__call {
  display: flex;
  align-items: center;
  gap: 17px;
  font-size: 20px;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
}
.team-details__info__call span {
  font-size: 24px;
  color: var(--grdeen-base, #1a9120);
}
.team-details__info__call a {
  color: inherit;
  line-height: 1.1;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.team-details__info__call a:hover {
  background-size: 100% 1px;
}
.team-details__info__call a:hover {
  color: var(--grdeen-base, #1a9120);
}

.team-form-one {
  position: relative;
  padding: 0 0 120px;
}
@media (max-width: 767px) {
  .team-form-one {
    padding: 0 0 80px;
  }
}
.team-form-one__title {
  text-align: center;
  font-size: 50px;
  color: var(--grdeen-black, #172000);
  font-weight: 900;
  line-height: 56px;
  margin: 0 0 44px;
}
@media (max-width: 767px) {
  .team-form-one__title {
    font-size: 40px;
    line-height: 45px;
    margin: 0 0 30px;
  }
}
.team-form-one__wrapper {
  position: relative;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-color: var(--grdeen-base, #1a9120);
  border-radius: 10px;
  padding: 86px 150px 61px;
}
@media (max-width: 1199px) {
  .team-form-one__wrapper {
    padding: 80px 50px;
  }
}
@media (max-width: 767px) {
  .team-form-one__wrapper {
    padding: 50px 20px;
  }
}
.team-form-one .form-one__group {
  gap: 30px;
}
@media (max-width: 767px) {
  .team-form-one .form-one__group {
    gap: 20px;
  }
}
.team-form-one .form-one .bootstrap-select > .dropdown-toggle,
.team-form-one .form-one input[type=text],
.team-form-one .form-one input[type=email],
.team-form-one .form-one textarea {
  background-color: var(--grdeen-white, #fff);
  height: 60px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  font-weight: 500;
  color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.7);
}
.team-form-one .form-one .bootstrap-select > .dropdown-toggle:focus,
.team-form-one .form-one input[type=text]:focus,
.team-form-one .form-one input[type=email]:focus,
.team-form-one .form-one textarea:focus {
  color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 1);
}
.team-form-one .form-one textarea {
  height: 183px;
}
.team-form-one .grdeen-btn {
  background-color: var(--grdeen-text-dark, #07370a);
  height: 54px;
  padding: 17px 38px;
}

.blog-card {
  position: relative;
}
.blog-card__image {
  position: relative;
  z-index: 3;
}
.blog-card__image__inner {
  overflow: hidden;
  position: relative;
}
.blog-card__image__inner:hover > a {
  opacity: 1;
  transform: translateY(0);
}
.blog-card__image img {
  transition: 0.5s;
  background-size: cover;
  width: 100%;
  border-radius: 4px 4px 0 0;
}
.blog-card__image__link {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.3);
  position: absolute;
  top: 0;
  left: 0;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transform: translateY(-30%);
  transition: opacity 500ms ease, transform 500ms ease;
}
.blog-card__image__link::before, .blog-card__image__link::after {
  content: "";
  width: 32px;
  height: 2px;
  background-color: var(--grdeen-white, #fff);
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.blog-card__image__link::after {
  transform: translate(-50%, -50%) rotate(90deg);
}
.blog-card__date {
  width: 70px;
  height: 64px;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  justify-content: center;
  text-align: center;
  align-items: center;
  color: var(--grdeen-white, #fff);
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-weight: 600;
  padding: 6px 10px;
  position: absolute;
  bottom: -14px;
  left: 30px;
  z-index: 10;
  flex-direction: column;
  border-radius: 11px;
}
.blog-card__date__num {
  font-size: 22px;
  line-height: 28px;
  display: block;
  margin-bottom: 3px;
}
.blog-card__date__month {
  display: block;
  font-size: 13px;
  line-height: 1;
}
.blog-card__content {
  background-color: var(--grdeen-white, #fff);
  padding: 35px 30px 24px 30px;
  position: relative;
  z-index: 2;
  transition: all 500ms ease;
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
  border-radius: 0 0 4px 4px;
}
.blog-card:hover .blog-card__content {
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.1);
}
.blog-card__title {
  margin: 0;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 20px;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  line-height: 30px;
  padding-bottom: 18px;
  margin-bottom: 20px;
  font-weight: 600;
}
.blog-card__title:hover {
  color: var(--grdeen-base, #1a9120);
}
@media (min-width: 768px) {
  .blog-card__title {
    font-size: 22px;
  }
}
@media (min-width: 992px) {
  .blog-card__title {
    font-size: 24px;
  }
}
.blog-card__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.blog-card__title a:hover {
  background-size: 100% 1px;
}
.blog-card__link {
  display: inline-flex;
  align-items: center;
  text-transform: uppercase;
  font-size: 13px;
  font-weight: 600;
  color: var(--grdeen-black, #172000);
  transition: all 500ms ease;
  line-height: 1;
  background-color: var(--grdeen-white4, #ecf2df);
  padding: 16px 23px;
  border-radius: 3px;
}
.blog-card__link__rightarrow {
  transition: all 500ms ease;
  margin-left: 5px;
  color: var(--grdeen-black, #172000);
  position: relative;
}
.blog-card__link:hover .blog-card__link__rightarrow {
  color: var(--grdeen-white, #fff);
  transform: translateX(8px);
}
.blog-card__link:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.blog-card__meta {
  display: flex;
  align-items: center;
  gap: 0 15px;
  margin: 0;
  margin-bottom: 11px;
}
.blog-card__meta li {
  color: var(--grdeen-text, #626f62);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.blog-card__meta li span {
  color: inherit;
  font-size: 14px;
  line-height: 1;
  margin-right: 6px;
}
.blog-card__meta li a {
  display: flex;
  align-items: center;
  color: inherit;
  transition: all 500ms ease;
}
.blog-card__meta li a:hover {
  color: var(--grdeen-base, #1a9120);
  text-shadow: 0 0 1px var(--grdeen-base, #1a9120);
}
.blog-card--two {
  position: relative;
}
.blog-card--two .blog-card__date {
  width: auto;
  height: 30px;
  border: 1px dashed var(--grdeen-white, #fff);
  font-size: 13px;
  bottom: -16px;
  border-radius: 0;
  padding: 6px 11px;
}
.blog-card--two .blog-card__content {
  padding-top: 37px;
  padding-bottom: 31px;
}
.blog-card--two .blog-card__title {
  margin-bottom: 14px;
}
.blog-card--two .blog-card-two__text {
  margin: 0;
  line-height: 28px;
}
.blog-card--two__shape {
  background-position: right top;
  background-repeat: no-repeat;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.blog-card--list {
  position: relative;
  background-color: #edf5ee;
  border-radius: 10px;
  overflow: hidden;
}
.blog-card--list:hover {
  box-shadow: 0px 10px 30px 0px rgba(0, 0, 0, 0.05);
}
@media (min-width: 1200px) {
  .blog-card--list {
    margin-right: 10px;
  }
}
.blog-card--list .blog-card__date {
  width: auto;
  height: 30px;
  border: 1px dashed var(--grdeen-white, #fff);
  font-size: 13px;
  bottom: -16px;
  border-radius: 0;
  padding: 6px 11px;
}
.blog-card--list .blog-card__meta {
  margin-bottom: 8px;
}
.blog-card--list .blog-card__content {
  padding: 37px 40px 36px;
  background-color: transparent;
  box-shadow: none;
}
@media (max-width: 1199px) {
  .blog-card--list .blog-card__content {
    padding-left: 30px;
    padding-right: 30px;
  }
}
.blog-card--list .blog-card__title {
  font-size: 30px;
  line-height: 42px;
  margin-bottom: 19px;
  padding: 0;
  border: none;
}
@media (max-width: 767px) {
  .blog-card--list .blog-card__title {
    font-size: 26px;
    line-height: 32px;
  }
}
.blog-card--list__text {
  margin: 0 0 25px;
  line-height: 32px;
}
.blog-card--list__rm {
  display: flex;
  align-items: center;
  line-height: 1.1;
  font-size: 14px;
  font-weight: 600;
  text-transform: capitalize;
  color: var(--grdeen-text-dark, #07370a);
}
.blog-card--list__rm span {
  display: inline-block;
  border-bottom: 1px solid currentColor;
}
.blog-card--list__rm i {
  transform: rotate(90deg);
  margin-left: 6px;
  font-size: 21px;
  position: relative;
  top: 1px;
}
.blog-card--list__rm:hover {
  color: var(--grdeen-base, #1a9120);
}
.blog-card--list__shape {
  background-position: right top;
  background-repeat: no-repeat;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.blog-one {
  padding: 112px 0;
}
@media (max-width: 991px) {
  .blog-one {
    padding: 80px 0;
  }
}
@media (max-width: 1200px) {
  .blog-one .container {
    max-width: 100%;
  }
}
.blog-one__sctwrap .sec-title {
  text-align: center;
}
.blog-one__sctwrap .sec-title__title {
  margin-top: 0;
}
.blog-one__carousel .owl-nav.disabled,
.blog-one__carousel .owl-dots.disabled {
  display: none !important;
}

.blog-two {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .blog-two {
    padding: 80px 0;
  }
}
@media (min-width: 1400px) {
  .blog-two .container {
    max-width: 1600px;
  }
}

.blog-page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .blog-page {
    padding: 80px 0;
  }
}

.blog-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 14px;
  margin-top: 40px;
}
@media (max-width: 767px) {
  .blog-pagination {
    margin-top: 20px;
    gap: 8px;
  }
}
.blog-pagination a,
.blog-pagination span {
  width: 50px;
  height: 50px;
  -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150.000000 150.000000"><g transform="translate(0.000000,150.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M740 1486 c0 -22 -23 -35 -78 -46 -43 -8 -57 -6 -82 8 -31 17 -31 17 -53 -10 -31 -38 -95 -69 -137 -66 -44 2 -60 -7 -61 -35 -1 -31 -82 -97 -119 -97 -30 0 -46 -24 -35 -52 8 -21 -50 -105 -86 -123 -29 -16 -40 -50 -17 -57 19 -7 -22 -127 -49 -145 -26 -17 -30 -48 -8 -57 23 -8 21 -133 -2 -159 -17 -18 -17 -20 7 -40 36 -31 63 -108 55 -156 -6 -35 -4 -39 24 -53 39 -18 82 -82 83 -122 1 -35 12 -45 48 -46 28 -1 110 -69 110 -93 0 -24 28 -40 60 -34 17 4 41 1 57 -8 15 -8 36 -16 46 -19 9 -3 22 -17 27 -31 11 -29 39 -33 73 -10 17 11 31 12 67 4 25 -6 67 -8 93 -4 36 4 55 1 77 -12 28 -16 30 -16 52 4 42 38 91 57 148 58 51 0 55 2 58 25 4 29 88 90 124 90 33 0 46 12 46 42 -1 32 67 118 92 118 28 0 43 25 30 50 -15 28 11 108 45 140 25 24 34 70 14 70 -8 0 -10 18 -6 58 8 76 15 92 38 92 13 0 19 7 19 24 0 19 -7 26 -30 31 -37 8 -43 28 -43 128 0 73 -1 78 -23 83 -30 8 -86 101 -74 123 12 22 -7 51 -33 51 -32 0 -116 76 -110 98 2 10 -5 26 -16 36 -17 15 -23 16 -36 6 -21 -17 -114 30 -137 70 -17 30 -41 39 -53 20 -8 -13 -86 -13 -119 0 -14 5 -28 20 -32 35 -4 18 -13 25 -30 25 -14 0 -24 -6 -24 -14z"/></g></svg>');
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 150.000000 150.000000"><g transform="translate(0.000000,150.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M740 1486 c0 -22 -23 -35 -78 -46 -43 -8 -57 -6 -82 8 -31 17 -31 17 -53 -10 -31 -38 -95 -69 -137 -66 -44 2 -60 -7 -61 -35 -1 -31 -82 -97 -119 -97 -30 0 -46 -24 -35 -52 8 -21 -50 -105 -86 -123 -29 -16 -40 -50 -17 -57 19 -7 -22 -127 -49 -145 -26 -17 -30 -48 -8 -57 23 -8 21 -133 -2 -159 -17 -18 -17 -20 7 -40 36 -31 63 -108 55 -156 -6 -35 -4 -39 24 -53 39 -18 82 -82 83 -122 1 -35 12 -45 48 -46 28 -1 110 -69 110 -93 0 -24 28 -40 60 -34 17 4 41 1 57 -8 15 -8 36 -16 46 -19 9 -3 22 -17 27 -31 11 -29 39 -33 73 -10 17 11 31 12 67 4 25 -6 67 -8 93 -4 36 4 55 1 77 -12 28 -16 30 -16 52 4 42 38 91 57 148 58 51 0 55 2 58 25 4 29 88 90 124 90 33 0 46 12 46 42 -1 32 67 118 92 118 28 0 43 25 30 50 -15 28 11 108 45 140 25 24 34 70 14 70 -8 0 -10 18 -6 58 8 76 15 92 38 92 13 0 19 7 19 24 0 19 -7 26 -30 31 -37 8 -43 28 -43 128 0 73 -1 78 -23 83 -30 8 -86 101 -74 123 12 22 -7 51 -33 51 -32 0 -116 76 -110 98 2 10 -5 26 -16 36 -17 15 -23 16 -36 6 -21 -17 -114 30 -137 70 -17 30 -41 39 -53 20 -8 -13 -86 -13 -119 0 -14 5 -28 20 -32 35 -4 18 -13 25 -30 25 -14 0 -24 -6 -24 -14z"/></g></svg>');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center center;
  mask-position: center center;
  -webkit-mask-size: cover;
  mask-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  background-color: #e7ebe7;
  color: var(--grdeen-text-dark, #07370a);
}
.blog-pagination a.current, .blog-pagination a:hover,
.blog-pagination span.current,
.blog-pagination span:hover {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
}
.blog-pagination a.next,
.blog-pagination span.next {
  width: auto;
  background-color: transparent;
  height: auto;
  mask: none;
  font-size: 18px;
  font-weight: 500;
}
.blog-pagination a.next:hover,
.blog-pagination span.next:hover {
  color: var(--grdeen-base, #1a9120);
}
.blog-pagination a.next i,
.blog-pagination span.next i {
  transform: rotate(90deg);
  margin-left: 2px;
  font-size: 26px;
  position: relative;
  top: 1px;
}
.blog-pagination a.prev,
.blog-pagination span.prev {
  width: auto;
  background-color: transparent;
  height: auto;
  mask: none;
  font-size: 18px;
  font-weight: 500;
}
.blog-pagination a.prev:hover,
.blog-pagination span.prev:hover {
  color: var(--grdeen-base, #1a9120);
}
.blog-pagination a.prev i,
.blog-pagination span.prev i {
  transform: rotate(-90deg);
  margin-right: 2px;
  font-size: 26px;
  position: relative;
  top: 1px;
}

/*--------------------------------------------------------------
# Form
--------------------------------------------------------------*/
.form-one__group {
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 17px 20px;
  margin: 0;
}
@media (min-width: 576px) {
  .form-one__group {
    grid-template-columns: repeat(2, 1fr);
  }
}
.form-one__control {
  border: none;
  width: auto;
  height: auto;
  border-radius: 0;
  padding: 0;
  position: relative;
}
.form-one__control__icon {
  position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-50%);
  font-size: 14px;
}
.form-one__control--full {
  grid-column-start: 1;
  grid-column-end: -1;
}
.form-one .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  width: 100%;
  height: 58px;
  display: flex;
  align-items: center;
}
.form-one .bootstrap-select > .dropdown-toggle {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  border: none;
  outline: none !important;
  color: var(--grdeen-text, #626f62);
  font-size: 14px;
}
.form-one .bootstrap-select > .dropdown-toggle,
.form-one input[type=text],
.form-one input[type=phone],
.form-one input[type=email],
.form-one textarea {
  display: block;
  width: 100%;
  height: 52px;
  background-color: var(--grdeen-white, #fff);
  color: #7d877d;
  font-size: 14px;
  font-weight: 400;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  border: none;
  outline: none;
  padding-left: 21px;
  padding-right: 21px;
  border-radius: 4px;
}
.form-one textarea {
  height: 128px;
  padding-top: 16px;
  resize: none;
}
.form-one .bootstrap-select > .dropdown-toggle {
  display: flex;
  align-items: center;
}
.form-one .bootstrap-select > .dropdown-toggle .filter-option {
  display: flex;
  align-items: center;
}

/*--------------------------------------------------------------
# Custom Cursor
--------------------------------------------------------------*/
.custom-cursor__cursor {
  width: 25px;
  height: 25px;
  border-radius: 100%;
  border: 1px solid var(--grdeen-base, #1a9120);
  -webkit-transition: all 200ms ease-out;
  transition: all 200ms ease-out;
  position: fixed;
  pointer-events: none;
  left: 0;
  top: 0;
  -webkit-transform: translate(calc(-50% + 5px), -50%);
  transform: translate(calc(-50% + 5px), -50%);
  z-index: 999991;
}
.custom-cursor__cursor-two {
  width: 10px;
  height: 10px;
  border-radius: 100%;
  background-color: var(--grdeen-base, #1a9120);
  opacity: 0.3;
  position: fixed;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  pointer-events: none;
  -webkit-transition: width 0.3s, height 0.3s, opacity 0.3s;
  transition: width 0.3s, height 0.3s, opacity 0.3s;
  z-index: 999991;
}
.custom-cursor__hover {
  background-color: var(--grdeen-base, #1a9120);
  opacity: 0.4;
}
.custom-cursor__innerhover {
  width: 25px;
  height: 25px;
  opacity: 0.4;
}

/*--------------------------------------------------------------
# Footer
--------------------------------------------------------------*/
.main-footer {
  position: relative;
  background-color: #10110c;
  padding-bottom: 40px;
}
.main-footer__top {
  padding-top: 50px;
  padding-bottom: 30px;
}
@media (max-width: 991px) {
  .main-footer__top {
    padding-top: 50px;
    padding-bottom: 20px;
  }
}
.main-footer__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: top center;
  mix-blend-mode: overlay;
}
.main-footer .container {
  position: relative;
}
@media (max-width: 1199px) {
  .main-footer .container {
    max-width: initial;
  }
}
.main-footer__bottom {
  text-align: center;
}
.main-footer__bottom__inner {
  padding: 17.25px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--grdeen-base, #1a9120);
  border-radius: 4px;
}
@media (max-width: 767px) {
  .main-footer__bottom__inner {
    flex-direction: column;
    gap: 15px 0;
  }
}
.main-footer__copyright {
  margin: 0;
  font-size: 15px;
  font-weight: 400;
  color: var(--grdeen-white, #fff);
}
.main-footer__social-row {
  display: flex;
  align-items: center;
}
.main-footer__social-row-text {
  font-size: 16px;
  line-height: 32px;
  font-weight: 500;
  color: var(--grdeen-white, #fff);
  margin-bottom: 0;
}
.main-footer__social-list {
  display: flex;
  align-items: center;
  padding-left: 14px;
  margin-bottom: 0;
  gap: 0 10px;
  list-style: none;
}
.main-footer__social-list li {
  list-style: none;
}
.main-footer__social-list li a {
  width: 35px;
  height: 35px;
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  color: rgba(var(--grdeen-text-rgb, 98, 111, 98));
  display: flex;
  font-size: 15px;
  align-items: center;
  justify-content: center;
  transition: all 0.4s;
}
.main-footer__social-list li a:hover {
  background-color: var(--grdeen-text-dark, #07370a);
  color: var(--grdeen-white, #fff);
}
.main-footer--two .main-footer__top, .main-footer--version2 .main-footer__top {
  padding-top: 102px;
}
@media (max-width: 767px) {
  .main-footer--two .main-footer__top, .main-footer--version2 .main-footer__top {
    padding-top: 62px;
  }
}

.footer-widget__col {
  width: 25%;
}
.footer-widget__col__col1 {
  width: 28.2%;
}
.footer-widget__col__col2 {
  width: 16.5%;
}
.footer-widget__col__col3 {
  width: 25.8%;
}
.footer-widget__col__col4 {
  width: 29.5%;
}
@media (max-width: 1199px) {
  .footer-widget__col {
    width: 50%;
  }
}
@media (max-width: 767px) {
  .footer-widget__col {
    width: 100%;
  }
}

.footer-widget {
  margin-bottom: 40px;
}
@media (max-width: 767px) {
  .footer-widget {
    margin-bottom: 30px;
  }
}
.footer-widget--about {
  max-width: 270px;
}
@media (max-width: 1199px) {
  .footer-widget--about {
    max-width: initial;
  }
}
.footer-widget__logo {
  display: inline-flex;
  max-width: 198px;
  margin-bottom: 23px;
}
.footer-widget__logo > img {
  width: 100%;
}
.footer-widget__experience-text {
  font-size: 16px;
  color: var(--grdeen-text, #626f62);
  line-height: 30px;
  margin-bottom: 34px;
  padding-right: 30px;
}
.footer-widget__newsletter {
  position: relative;
  width: 100%;
}
@media (max-width: 1199px) {
  .footer-widget__newsletter {
    max-width: initial;
  }
}
.footer-widget__newsletter button[type=submit] {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;
  width: 89px;
  height: 100%;
  border: none;
  outline: none;
  color: var(--grdeen-white, #fff);
  font-size: 13px;
  font-weight: 500;
  line-height: 1;
  text-transform: uppercase;
  transition: all 500ms ease;
  border-radius: 0 3px 3px 0;
  background-color: var(--grdeen-base, #1a9120);
}
.footer-widget__newsletter button[type=submit]:hover {
  background-color: var(--grdeen-text-dark, #07370a);
  color: var(--grdeen-white, #fff);
}
.footer-widget__mail-address {
  position: relative;
}
.footer-widget__mail-address input[type=text],
.footer-widget__mail-address input[type=email] {
  width: 100%;
  display: block;
  border: none;
  outline: none;
  height: 58px;
  background-color: var(--grdeen-white, #fff);
  color: #848484;
  font-size: 13px;
  font-weight: 400;
  padding-left: 18px;
  padding-right: 92px;
  transition: all 500ms ease;
  border-radius: 4px;
}
.footer-widget__mail-address input[type=text]:focus,
.footer-widget__mail-address input[type=email]:focus {
  color: var(--grdeen-text, #626f62);
}
.footer-widget__mail-icon {
  position: absolute;
  top: 50%;
  left: 16px;
  transform: translateY(-50%);
  color: var(--grdeen-white, #fff);
  height: 100%;
  display: flex;
  align-items: center;
}
.footer-widget__mail-icon > i {
  font-size: 19px;
}
.footer-widget__title {
  font-size: 20px;
  font-weight: 600;
  color: var(--grdeen-white, #fff);
  margin: 0;
  margin-bottom: 42px;
}
.footer-widget__title--newsletter {
  margin-bottom: 22px;
}
@media (max-width: 767px) {
  .footer-widget__title--newsletter {
    margin-bottom: 22px;
  }
}
@media (max-width: 767px) {
  .footer-widget__title {
    font-size: 18px;
    margin-bottom: 26px;
  }
}
.footer-widget__info, .footer-widget__links {
  margin-top: -10px;
  margin-bottom: 0;
}
.footer-widget__info li, .footer-widget__links li {
  font-size: 16px;
  color: var(--grdeen-text, #626f62);
  font-weight: 400;
  line-height: 30px;
  position: relative;
  padding-left: 27px;
  margin-bottom: 10px;
}
@media (max-width: 767px) {
  .footer-widget__info li, .footer-widget__links li {
    margin-bottom: 4px;
  }
}
.footer-widget__info li:last-child, .footer-widget__links li:last-child {
  margin-bottom: 0;
}
.footer-widget__info li::before, .footer-widget__links li::before {
  font-family: "icomoon" !important;
  content: "\e93b";
  position: absolute;
  width: auto;
  height: auto;
  top: 1px;
  left: 0;
  font-size: 15px;
  color: inherit;
  transition: all 0.5s ease;
}
.footer-widget__info li:hover::before, .footer-widget__links li:hover::before {
  color: var(--grdeen-white, #fff);
}
.footer-widget__info li a, .footer-widget__links li a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.footer-widget__info li a:hover, .footer-widget__links li a:hover {
  background-size: 100% 1px;
}
.footer-widget__info li a:hover, .footer-widget__links li a:hover {
  color: var(--grdeen-white, #fff);
}
.footer-widget__gallerywrap {
  gap: 10px;
}
.footer-widget__gallerywrap__img {
  width: calc((100% - 20px) / 3);
}
.footer-widget__gallerywrap__img img {
  width: 100% !important;
  border-radius: 3px !important;
}
.footer-widget--blog {
  padding-left: 25px;
}
@media (max-width: 1199px) {
  .footer-widget--blog {
    padding-left: 0;
  }
}
.footer-widget__post-col {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #2e3024;
  padding-bottom: 27px;
}
@media (max-width: 767px) {
  .footer-widget__post-col {
    padding-bottom: 22px;
  }
}
.footer-widget__post-col:last-child {
  border-bottom: 0;
  padding-bottom: 0;
}
.footer-widget__post-col + .footer-widget__post-col {
  padding-top: 26px;
}
@media (max-width: 767px) {
  .footer-widget__post-col + .footer-widget__post-col {
    padding-top: 22px;
  }
}
.footer-widget__post-img {
  width: 78px;
}
.footer-widget__post-img > img {
  width: 100% !important;
  border-radius: 3px !important;
}
.footer-widget__post-info {
  width: calc((100% - 78px) / 1);
  padding-left: 19px;
}
@media (max-width: 767px) {
  .footer-widget__post-info {
    padding-left: 12px;
  }
}
.footer-widget__post-date {
  font-size: 13px;
  line-height: 1;
  font-weight: 400;
  display: block;
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.5);
  margin-bottom: 9px;
}
.footer-widget__post-date > span {
  padding-left: 5px;
}
.footer-widget__post-heading {
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 16px;
  line-height: 30px;
  font-weight: 600;
  color: var(--grdeen-white, #fff);
  margin-bottom: 0;
}
@media (max-width: 767px) {
  .footer-widget__post-heading {
    font-size: 15px;
    line-height: 23px;
  }
}
.footer-widget__post-heading > a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.footer-widget__post-heading > a:hover {
  background-size: 100% 1px;
}

/*--------------------------------------------------------------
# Contact
--------------------------------------------------------------*/
.contact-one {
  position: relative;
  padding: 112px 0 0;
}
@media (max-width: 767px) {
  .contact-one {
    padding: 75px 0 0;
  }
}
.contact-one__title {
  text-align: center;
  font-size: 43px;
  line-height: 50px;
  font-weight: 700;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin-bottom: 27px;
}
@media (max-width: 767px) {
  .contact-one__title {
    font-size: 35px;
    line-height: 45px;
  }
}
.contact-one__text {
  margin: 0;
  font-size: 18px;
  text-align: center;
  margin-bottom: 68px;
}
@media (max-width: 767px) {
  .contact-one__text {
    margin-bottom: 50px;
  }
}
.contact-one__info-wrapper {
  position: relative;
  z-index: 2;
  background-color: #1c391e;
  border-radius: 10px;
  background-position: bottom center;
  background-repeat: no-repeat;
  padding: 52px 0 62px;
}
.contact-one__info {
  margin: 0;
  padding: 0;
  position: relative;
  text-align: center;
  border-right: 1px solid rgb(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}
.contact-one__info__icon {
  width: 78px;
  height: 78px;
  border-radius: 50%;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  transition: all 500ms ease;
  color: var(--grdeen-white, #fff);
  margin: 0 auto;
}
.contact-one__info:hover .contact-one__info__icon {
  background-color: var(--grdeen-black, #172000);
  color: var(--grdeen-white, #fff);
}
.contact-one__info__title {
  font-size: 24px;
  font-weight: 600;
  color: var(--grdeen-white, #fff);
  margin: 33px 0 16px;
}
.contact-one__info__text {
  margin: 0 auto;
  max-width: 225px;
  font-size: 16px;
  font-weight: 500;
  color: var(--grdeen-white, #fff);
  line-height: 28px;
}
.contact-one__info__text a {
  display: inline-block;
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.contact-one__info__text a:hover {
  background-size: 100% 1px;
}
.contact-one__container {
  max-width: 1430px;
}
.contact-one__wrapper {
  position: relative;
  background-color: #091e0a;
  background-blend-mode: overlay;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  border-radius: 10px;
  padding: 276px 0 69px;
  margin-top: -169px;
}
@media (max-width: 991px) {
  .contact-one__wrapper {
    padding-left: 30px;
    padding-right: 30px;
  }
}
@media (max-width: 767px) {
  .contact-one__wrapper {
    padding: 250px 20px 69px;
  }
}
.contact-one__form {
  max-width: 873px;
  margin: 0 auto;
}
.contact-one__form .form-one__group {
  gap: 30px;
}
@media (max-width: 767px) {
  .contact-one__form .form-one__group {
    gap: 20px;
  }
}
.contact-one__form .bootstrap-select > .dropdown-toggle,
.contact-one__form input[type=text],
.contact-one__form input[type=email],
.contact-one__form textarea {
  background-color: var(--grdeen-white, #fff);
  height: 62px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  font-weight: 400;
  color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.7);
}
.contact-one__form .bootstrap-select > .dropdown-toggle:focus,
.contact-one__form input[type=text]:focus,
.contact-one__form input[type=email]:focus,
.contact-one__form textarea:focus {
  color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 1);
}
.contact-one__form .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.4em solid;
  border-right: 0.4em solid transparent;
  border-bottom: 0;
  border-left: 0.4em solid transparent;
}
.contact-one__form textarea {
  height: 183px;
}
.contact-one__form .grdeen-btn {
  height: 54px;
  padding: 17px 38px;
}

/*--------------------------------------------------------------
# Topbar
--------------------------------------------------------------*/
.topbar-one {
  display: none;
  background-color: var(--grdeen-white, #fff);
  padding-bottom: 10px;
}
@media (min-width: 768px) {
  .topbar-one {
    display: block;
  }
}
.topbar-one .container-fluid {
  max-width: 1814px;
}
.topbar-one__inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 10px;
  padding-bottom: 10px;
}
@media (min-width: 992px) {
  .topbar-one__inner {
    flex-direction: row;
  }
}
.topbar-one__info {
  display: flex;
  align-items: center;
  margin: 0;
}
.topbar-one__info__item, .topbar-one__right__list li {
  display: flex;
  align-items: center;
  color: var(--grdeen-text, #626f62);
  font-weight: 500;
  font-size: 13px;
}
.topbar-one__info__item a, .topbar-one__right__list li a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.topbar-one__info__item a:hover, .topbar-one__right__list li a:hover {
  background-size: 100% 1px;
}
.topbar-one__info__item + .topbar-one__info__item, .topbar-one__right__list li + .topbar-one__info__item, .topbar-one__right__list .topbar-one__info__item + li, .topbar-one__right__list li + li {
  margin-left: 40px;
}
@media (max-width: 1200px) {
  .topbar-one__info__item + .topbar-one__info__item, .topbar-one__right__list li + .topbar-one__info__item, .topbar-one__right__list .topbar-one__info__item + li, .topbar-one__right__list li + li {
    margin-left: 30px;
  }
}
.topbar-one__info__iconwrap, .topbar-one__right__iconwrap {
  font-size: 14px;
  color: var(--grdeen-base, #1a9120);
  position: relative;
  top: 1px;
  margin-right: 5px;
}
.topbar-one__right {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
@media (min-width: 992px) {
  .topbar-one__right {
    margin-top: 0;
    margin-left: auto;
  }
}
.topbar-one__right__iconwrap {
  margin-right: 7px;
  font-size: 15px;
}
.topbar-one__right__list {
  margin: 0;
  padding-left: 48px;
  display: flex;
  align-items: center;
}
@media (max-width: 1200px) {
  .topbar-one__right__list {
    padding-left: 32px;
  }
}
@media (max-width: 1150px) {
  .topbar-one__right__list {
    display: none;
  }
}
.topbar-one__right__list li {
  position: relative;
  margin: 0;
  padding: 0 11px;
}
.topbar-one__right__list li a {
  color: inherit;
}
.topbar-one__right__list li + li {
  margin: 0;
}
.topbar-one__right__list li + li::after {
  content: " / ";
  position: absolute;
  width: auto;
  height: 100%;
  top: 0;
  bottom: 0;
  left: -1px;
  z-index: 1;
  margin: auto;
}
.topbar-one__text {
  margin: 0;
  color: var(--grdeen-text, #626f62);
  font-weight: 500;
  font-size: 13px;
  line-height: 1;
}
.topbar-one__social {
  display: flex;
  align-items: center;
  margin-left: 37px;
}
@media (max-width: 1200px) {
  .topbar-one__social {
    margin-left: 16px;
  }
}
.topbar-one__social a {
  width: 35px;
  height: 35px;
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.1);
  transition: all 500ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.topbar-one__social a:hover {
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32));
  color: var(--grdeen-white, #fff);
}
.topbar-one__social a + a {
  margin-left: 10px;
}
.topbar-one--one_only {
  background-color: var(--grdeen-white, #fff);
  padding-bottom: 0;
}
.topbar-one--one_only .topbar-one__logo {
  width: 198px;
}
@media (max-width: 1250px) {
  .topbar-one--one_only .topbar-one__logo {
    width: 162px;
  }
}
@media (max-width: 1200px) {
  .topbar-one--one_only .topbar-one__logo {
    display: none;
  }
}
.topbar-one--one_only .topbar-one__logo img {
  width: 100% !important;
}
.topbar-one--one_only .topbar-one__inner {
  padding-top: 24px;
  padding-bottom: 25px;
  border-bottom: 0;
}
@media (max-width: 1199px) {
  .topbar-one--one_only .topbar-one__inner {
    padding-top: 18px;
    padding-bottom: 20px;
  }
}
.topbar-one--one_only .topbar-one__info {
  margin-left: auto;
  margin-right: 0;
  padding-left: 36px;
}
@media (max-width: 1540px) {
  .topbar-one--one_only .topbar-one__info {
    padding-left: 20px;
  }
}
@media (max-width: 1300px) {
  .topbar-one--one_only .topbar-one__info {
    padding-left: 15px;
  }
}
@media (max-width: 1200px) {
  .topbar-one--one_only .topbar-one__info {
    margin: 0;
    padding-left: 0;
  }
}
.topbar-one--one_only .topbar-one__info .topbar-one__info__item, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li {
  padding: 3.5px 80px;
  transition: all 0.5s ease;
}
@media (max-width: 1540px) {
  .topbar-one--one_only .topbar-one__info .topbar-one__info__item, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li {
    padding-left: 42px;
    padding-right: 42px;
  }
}
@media (max-width: 1366px) {
  .topbar-one--one_only .topbar-one__info .topbar-one__info__item, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li {
    padding-left: 25px;
    padding-right: 25px;
  }
}
@media (max-width: 1200px) {
  .topbar-one--one_only .topbar-one__info .topbar-one__info__item, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li {
    padding-left: 22px;
    padding-right: 22px;
  }
}
.topbar-one--one_only .topbar-one__info .topbar-one__info__item:first-child, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li:first-child, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li:first-child {
  padding-left: 0;
}
.topbar-one--one_only .topbar-one__info .topbar-one__info__item:last-child, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li:last-child, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li:last-child {
  padding-right: 0;
}
.topbar-one--one_only .topbar-one__info .topbar-one__info__item:hover .topbar-one__info__iconwrap, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li:hover .topbar-one__info__iconwrap, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li:hover .topbar-one__info__iconwrap, .topbar-one--one_only .topbar-one__info .topbar-one__info__item:hover .topbar-one__right__iconwrap, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li:hover .topbar-one__right__iconwrap, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li:hover .topbar-one__right__iconwrap {
  background-color: var(--grdeen-base, #1a9120);
}
.topbar-one--one_only .topbar-one__info .topbar-one__info__item + .topbar-one__info__item, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li + .topbar-one__info__item, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li + .topbar-one__info__item, .topbar-one--one_only .topbar-one__info .topbar-one__right__list .topbar-one__info__item + li, .topbar-one__right__list .topbar-one--one_only .topbar-one__info .topbar-one__info__item + li, .topbar-one--one_only .topbar-one__info .topbar-one__right__list li + li, .topbar-one__right__list .topbar-one--one_only .topbar-one__info li + li {
  border-left: 1px solid var(--grdeen-border-color, #e7e7e7);
  margin-left: 0;
}
.topbar-one--one_only .topbar-one__info__iconwrap, .topbar-one--one_only .topbar-one__right__iconwrap {
  width: 51px;
  height: 54px;
  border-radius: 8px 0 8px 8px;
  background-color: var(--grdeen-text-dark, #07370a);
  font-size: 22px;
  line-height: 1;
  color: var(--grdeen-white, #fff);
  margin-right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s ease;
}
.topbar-one--one_only .topbar-one__info__iconwrap svg, .topbar-one--one_only .topbar-one__right__iconwrap svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
@media (max-width: 1300px) {
  .topbar-one--one_only .topbar-one__info__iconwrap, .topbar-one--one_only .topbar-one__right__iconwrap {
    width: 48px;
    height: 50px;
  }
}
.topbar-one--one_only .topbar-one__info__address {
  flex-direction: column;
  padding-left: 20px;
}
@media (max-width: 1540px) {
  .topbar-one--one_only .topbar-one__info__address {
    padding-left: 15px;
  }
}
@media (max-width: 1300px) {
  .topbar-one--one_only .topbar-one__info__address {
    padding-left: 10px;
  }
}
.topbar-one--one_only .topbar-one__info__address__text {
  color: var(--grdeen-black, #172000);
  font-weight: 400;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-size: 14px;
  line-height: 21px;
  display: block;
}
.topbar-one--one_only .topbar-one__info__address > a {
  color: var(--grdeen-black, #172000);
  font-weight: 500;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-size: 15px;
  line-height: 26px;
  display: block;
}
@media (max-width: 991px) {
  .topbar-one--one_only .topbar-one__info__address > a {
    font-size: 16px;
    line-height: 22px;
  }
}
.topbar-one--one_only .topbar-one__social {
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.1);
  border-left: 0;
  padding: 8.5px 15px 8.5px 28px;
  border-radius: 4px;
  margin-left: 15px;
}
@media (max-width: 1540px) {
  .topbar-one--one_only .topbar-one__social {
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (max-width: 1199px) {
  .topbar-one--one_only .topbar-one__social {
    display: none;
  }
}
.topbar-one--one_only .topbar-one__social a {
  width: 38px;
  height: 37px;
  border-radius: 3px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--grdeen-black, #172000);
  font-size: 16px;
}
.topbar-one--one_only .topbar-one__social a:hover {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-green, #1f9a26);
}
.topbar-one--one_only .topbar-one__social a + a {
  margin-left: 6.5px;
}
.topbar-one--three {
  background-color: #031c05;
}
.topbar-one--three .topbar-one__info__item, .topbar-one__right__list .topbar-one--three li,
.topbar-one--three .topbar-one__right__list li,
.topbar-one--three .topbar-one__text {
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.7);
}
.topbar-one--three .topbar-one__social a {
  background-color: var(--grdeen-white, #fff);
}
.topbar-one--three .topbar-one__social a:hover {
  background-color: var(--grdeen-base, #1a9120);
}

/*--------------------------------------------------------------
# Navigations
--------------------------------------------------------------*/
.main-header {
  background-color: transparent;
  position: relative;
  z-index: 99;
}
.main-header .container-fluid {
  max-width: 1814px;
}
.main-header__inner {
  display: flex;
  background-color: var(--grdeen-base, #1a9120);
  border-radius: 5px;
  padding: 0;
  position: relative;
}
.main-header__wellcome {
  background-color: var(--grdeen-gray, #f6f7f2);
  padding: 10px 26px 10px 28px;
  position: relative;
  height: 89px;
  margin-top: -9px;
}
@media (max-width: 1200px) {
  .main-header__wellcome {
    display: none !important;
  }
}
@media (max-width: 1600px) {
  .main-header__wellcome {
    padding-left: 20px;
    padding-right: 20px;
  }
}
.main-header__wellcome::after {
  content: "";
  position: absolute;
  width: 57px;
  height: 100%;
  right: -56px;
  top: 0;
  z-index: 1;
  -webkit-clip-path: polygon(0 101%, 100% 0, 0 0);
  clip-path: polygon(0 101%, 100% 0, 0 0);
  background-color: var(--grdeen-gray, #f6f7f2);
}
.main-header__wellcome__tagline {
  color: var(--grdeen-black, #172000);
  font-weight: 600;
  font-size: 16px;
  line-height: 26px;
  margin-bottom: 0;
}
@media (max-width: 1500px) {
  .main-header__wellcome__tagline {
    display: none;
  }
}
.main-header__wellcome__btn {
  font-weight: 500;
  line-height: 1;
  padding: 8.5px 17px;
  letter-spacing: 0.32px;
  margin-left: 19px;
}
@media (max-width: 1500px) {
  .main-header__wellcome__btn {
    margin-left: 0;
  }
}
.main-header__logo {
  display: flex;
  width: 258px;
  height: 109px;
  align-items: center;
  justify-content: center;
  background-color: var(--grdeen-white, #fff);
  padding: 10px 30px;
  margin-bottom: -8px;
  margin-top: -11px;
  border-radius: 0 0 0 4px;
  position: relative;
}
.main-header__logo::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  display: inline-block;
  z-index: 99;
  border-right: 10px solid transparent;
  border-top: 8px solid rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.8);
  right: -10px;
  bottom: 0;
}
@media (max-width: 767px) {
  .main-header__logo {
    background-color: var(--grdeen-white, #fff);
    width: auto;
    padding: 18px 12px;
    margin: 0;
    height: auto;
  }
}
.main-header__logo img {
  width: 100%;
}
@media (max-width: 767px) {
  .main-header__logo img {
    max-width: 140px;
  }
}
@media (min-width: 768px) {
  .main-header__logo .mobile-nav__btn {
    margin-left: 30px;
  }
}
.main-header--one_only .main-header__logo {
  display: none;
}
@media (max-width: 1200px) {
  .main-header--one_only .main-header__logo {
    display: flex;
    background-color: var(--grdeen-white, #fff);
    width: auto;
    padding-right: 15px;
  }
}
@media (max-width: 1200px) {
  .main-header--one_only .main-header__logo img {
    width: 100%;
    max-width: 170px;
  }
}
.main-header__btn {
  display: none;
  color: var(--grdeen-black, #172000);
  padding: 13.3px 42px;
  background-color: var(--grdeen-white, #fff);
  margin-right: 30px;
}
@media (max-width: 1700px) {
  .main-header__btn {
    margin-right: 20px;
  }
}
@media (max-width: 1400px) {
  .main-header__btn {
    margin-right: 15px;
    padding-left: 38px;
    padding-right: 38px;
  }
}
@media (min-width: 768px) {
  .main-header__btn {
    display: inline-flex;
  }
}
.main-header__right {
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-right: 0;
  position: relative;
}
@media (max-width: 1430px) {
  .main-header__right {
    padding-right: 26px;
  }
}
.main-header__cart, .main-header__search {
  transition: all 500ms ease;
  padding: 10px 27px;
}
@media (max-width: 1600px) {
  .main-header__cart, .main-header__search {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.main-header__cart__icon, .main-header__search__icon {
  transition: all 500ms ease;
  font-size: 24px;
  line-height: 1em;
  color: var(--grdeen-white, #fff);
}
.main-header__cart__icon svg, .main-header__search__icon svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
.main-header__cart__icon:hover, .main-header__search__icon:hover {
  color: var(--grdeen-black, #172000);
}
.main-header__search {
  padding-left: 0;
}
.main-header__cart {
  position: relative;
  border-left: 1px solid rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.3);
  padding-right: 0;
}
.main-header__cart__count {
  position: absolute;
  width: 19px;
  height: 19px;
  border-radius: 50%;
  top: 7px;
  right: -11px;
  z-index: 1;
  background-color: var(--grdeen-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--grdeen-black, #172000);
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-weight: 400;
  font-size: 16px;
  line-height: 1;
}
.main-header__nav {
  padding-left: 49px;
}
@media (max-width: 1700px) {
  .main-header__nav {
    padding-left: 25px;
  }
}
@media (max-width: 1500px) {
  .main-header__nav {
    margin-left: auto;
    margin-right: auto;
    padding-left: 24px;
  }
}
.main-header__helpline {
  background-color: var(--grdeen-white, #fff);
  display: flex;
  align-items: center;
  border-radius: 0 4px 4px 0;
  margin-left: 28px;
  padding-left: 25px;
  padding-right: 32px;
  height: 100%;
  position: relative;
}
@media (max-width: 1700px) {
  .main-header__helpline {
    padding: 0 20px;
  }
}
@media (max-width: 1430px) {
  .main-header__helpline {
    display: none;
  }
}
.main-header__helpline__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 1;
  margin: auto 0;
  background-repeat: no-repeat;
  background-size: 72px auto;
  background-position: right 14px center;
}
.main-header__helpline__icon {
  width: 51px;
  height: 54px;
  border-radius: 8px 0 8px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  background-color: var(--grdeen-text-dark, #07370a);
  color: var(--grdeen-white, #fff);
  transition: all 0.5s ease;
  position: relative;
  z-index: 2;
}
@media (max-width: 1500px) {
  .main-header__helpline__icon {
    display: none;
  }
}
.main-header__helpline__phonewrap {
  padding-left: 19px;
  position: relative;
  z-index: 2;
}
@media (max-width: 1700px) {
  .main-header__helpline__phonewrap {
    padding-left: 11px;
  }
}
@media (max-width: 1500px) {
  .main-header__helpline__phonewrap {
    padding-left: 0;
  }
}
.main-header__helpline__text {
  margin: 0;
  font-weight: 500;
  color: rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.8);
  margin-bottom: 3px;
}
.main-header__helpline__tel {
  display: block;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 700;
  font-size: 20px;
  line-height: 1;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.main-header__helpline__tel:hover {
  background-size: 100% 1px;
}
.main-header__helpline:hover .main-header__helpline__icon {
  background-color: var(--grdeen-base, #1a9120);
  border-radius: 8px;
}
.main-header--only-one .main-header__right {
  background-color: var(--grdeen-green, #1f9a26);
  padding: 9.5px 20px 9.5px 9px;
  border-radius: 0 5px 5px 6px;
  height: 97px;
  margin-top: -8px;
  margin-bottom: -9px;
}
@media (max-width: 1200px) {
  .main-header--only-one .main-header__right {
    padding-left: 45px;
    padding-right: 20px;
  }
}
@media (max-width: 480px) {
  .main-header--only-one .main-header__right {
    height: 90px;
    margin: 0;
    border-radius: 0;
    padding-left: 50px;
  }
}
.main-header--only-one .main-header__right::before {
  content: "";
  position: absolute;
  width: 78px;
  height: 100%;
  left: -74px;
  top: 0;
  z-index: 1;
  -webkit-clip-path: polygon(0 0, 100% 100%, 100% 0);
  clip-path: polygon(0 0, 100% 100%, 100% 0);
  background-color: var(--grdeen-green, #1f9a26);
  border-radius: 0 0 6px 0;
}
@media (max-width: 480px) {
  .main-header--only-one .main-header__right::before {
    content: none;
  }
}
.main-header--only-one .main-header__right::after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  display: inline-block;
  z-index: 99;
  border-right: 10px solid transparent;
  border-top: 12px solid var(--grdeen-green, #1f9a26);
  left: -80px;
  top: 2px;
  transform: rotate(50deg);
}
@media (max-width: 480px) {
  .main-header--only-one .main-header__right::after {
    content: none;
  }
}
.main-header--only-one .main-header__btn {
  margin-left: 50px;
  margin-right: 0;
}
@media (max-width: 1600px) {
  .main-header--only-one .main-header__btn {
    margin-left: 25px;
    padding-left: 22px;
    padding-right: 22px;
  }
}
.main-header--only-one .main-header__nav {
  padding-left: 92px;
}
@media (max-width: 1740px) {
  .main-header--only-one .main-header__nav {
    padding-left: 60px;
  }
}
@media (max-width: 1500px) {
  .main-header--only-one .main-header__nav {
    margin-left: auto;
    margin-right: auto;
    padding-left: 28px;
  }
}
.main-header--only-one .main-menu .main-menu__list > li {
  padding-top: 28px;
  padding-bottom: 28px;
}
@media (max-width: 1700px) {
  .main-header--only-one .main-menu .main-menu__list > li {
    margin-left: 28px;
  }
}
.main-header--two {
  background-color: transparent;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 9;
  width: 100%;
  border-bottom: 1px solid RGBA(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}
.main-header--two.sticky-header--cloned {
  background-color: var(--grdeen-black, #172000);
  border: none;
}
.main-header--two.sticky-header--cloned .main-menu .main-menu__list > li {
  padding-top: 35.25px;
  padding-bottom: 35.25px;
}
.main-header--two .mobile-nav__btn span {
  background-color: var(--grdeen-white, #fff);
}
.main-header--two .main-header__btn::after,
.main-header--two .main-header__btn {
  background-color: var(--grdeen-white, #fff);
}
.main-header--two .main-header__btn:hover {
  color: var(--grdeen-black, #172000);
}
.main-header--two .container-fluid {
  max-width: 100%;
}
.main-header--two .main-header__inner {
  background-color: transparent;
}
.main-header--two .main-menu .main-menu__list > li {
  padding-top: 51.25px;
  padding-bottom: 51.25px;
}
.main-header--two .main-menu .main-menu__list > li > a {
  color: var(--grdeen-white, #fff);
}
.main-header--two .main-menu .main-menu__list > li.current > a,
.main-header--two .main-menu .main-menu__list > li:hover > a {
  color: var(--grdeen-base, #1a9120);
}
.main-header--two .main-header__cart,
.main-header--two .main-header__search {
  color: var(--grdeen-white, #fff);
}
.main-header--two .main-header__cart:hover,
.main-header--two .main-header__search:hover {
  color: var(--grdeen-base, #1a9120);
}
.main-header--two .main-header__right {
  border-color: RGBA(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}
.main-header--three .main-header__helpline {
  background-color: #031c05;
}
.main-header--three .main-header__helpline__text, .main-header--three .main-header__helpline__tel {
  color: var(--grdeen-white, #fff);
}

.sticky-header--cloned {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  top: 0;
  background-color: var(--grdeen-white, #fff);
  transform: translateY(-100%);
  box-shadow: 0px 3px 18px rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.07);
  transition: 0.6s cubic-bezier(0.24, 0.74, 0.58, 1);
  visibility: hidden;
  transition: transform 500ms ease, visibility 500ms ease;
}
.sticky-header--cloned.active {
  transform: translateY(0%);
  visibility: visible;
}

.mobile-nav__btn {
  width: 24px;
  display: flex;
  align-items: center;
  flex-direction: column;
  flex-wrap: wrap;
  cursor: pointer;
  z-index: 3;
}
@media (max-width: 1199px) {
  .mobile-nav__btn {
    margin-left: -50px;
    margin-right: 20px;
  }
}
@media (max-width: 767px) {
  .mobile-nav__btn {
    margin-left: -40px;
  }
}
@media (min-width: 1200px) {
  .mobile-nav__btn {
    display: none;
  }
}
.mobile-nav__btn span {
  width: 100%;
  height: 2px;
  background-color: var(--grdeen-white, #fff);
}
.mobile-nav__btn span:nth-child(2) {
  margin-top: 4px;
  margin-bottom: 4px;
}

.main-menu {
  /* after third level no menu */
}
.main-menu .main-menu__list,
.main-menu .main-menu__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
  align-items: center;
  display: none;
}
@media (min-width: 1200px) {
  .main-menu .main-menu__list,
  .main-menu .main-menu__list ul {
    display: flex;
  }
}
.main-menu .main-menu__list > li {
  padding-top: 33px;
  padding-bottom: 33px;
  position: relative;
}
.main-menu .main-menu__list > li.dropdown > a {
  position: relative;
}
.main-menu .main-menu__list > li + li {
  margin-left: 41px;
}
@media (max-width: 1700px) {
  .main-menu .main-menu__list > li + li {
    margin-left: 31px;
  }
}
@media (max-width: 1300px) {
  .main-menu .main-menu__list > li + li {
    margin-left: 28px;
  }
}
.main-menu .main-menu__list > li > a {
  color: var(--grdeen-white, #fff);
  font-size: 16px;
  display: flex;
  align-items: center;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-weight: 500;
  position: relative;
  transition: all 500ms ease;
}
.main-menu .main-menu__list > li.current > a,
.main-menu .main-menu__list > li:hover > a {
  color: var(--grdeen-black, #172000);
  text-shadow: 0 0 0.5px var(--grdeen-black, #172000);
}
.main-menu .main-menu__list li ul {
  position: absolute;
  top: 100%;
  left: -25px;
  min-width: 270px;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  opacity: 0;
  visibility: hidden;
  transform-origin: top center;
  transform: scaleY(0) translateZ(100px);
  transition: opacity 500ms ease, visibility 500ms ease, transform 700ms ease;
  z-index: 99;
  background-color: var(--grdeen-white, #fff);
  padding: 20px;
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.07);
  border-radius: 0 0 6px 6px;
}
.main-menu .main-menu__list li:hover > ul {
  opacity: 1;
  visibility: visible;
  transform: scaleY(1) translateZ(0px);
}
.main-menu .main-menu__list > .megamenu {
  position: static;
}
.main-menu .main-menu__list > .megamenu > ul {
  top: 100%;
  left: 0;
  right: 0;
  background-color: transparent;
  box-shadow: none;
  padding: 0;
}
.main-menu .main-menu__list li ul li {
  flex: 1 1 100%;
  width: 100%;
  position: relative;
}
.main-menu .main-menu__list li ul li > a {
  font-size: 15px;
  line-height: 26px;
  color: var(--grdeen-text, #626f62);
  font-weight: 500;
  display: flex;
  padding: 7px 20px;
  transition: 400ms;
  margin-bottom: 4px;
  border-radius: 4px;
}
.main-menu .main-menu__list li ul li > a::after {
  position: absolute;
  right: 20px;
  top: 8px;
  border-radius: 0;
  font-size: 14px;
  font-weight: 900;
  font-family: "Font Awesome 5 Free";
  content: "\f0da";
  color: currentColor;
  visibility: hidden;
  opacity: 0;
  transform: scale(0);
}
.main-menu .main-menu__list li ul li.current > a,
.main-menu .main-menu__list li ul li:hover > a {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.main-menu .main-menu__list li ul li.current > a::after,
.main-menu .main-menu__list li ul li:hover > a::after {
  visibility: visible;
  opacity: 1;
  transform: scale(1);
}
.main-menu .main-menu__list li ul li > ul {
  top: 0;
  left: calc(100% + 20px);
}
.main-menu .main-menu__list li ul li > ul.right-align {
  top: 0;
  left: auto;
  right: 100%;
}
.main-menu .main-menu__list li ul li > ul ul {
  display: none;
}

@media (min-width: 1200px) and (max-width: 1400px) {
  .main-menu__list li:nth-last-child(1) ul li > ul,
  .main-menu__list li:nth-last-child(2) ul li > ul {
    left: auto;
    right: calc(100% + 20px);
  }
}
/*--------------------------------------------------------------
# Megamenu Popup
--------------------------------------------------------------*/
.mobile-nav__container .main-menu__list > .megamenu.megamenu-clickable > ul,
.main-menu .main-menu__list > .megamenu.megamenu-clickable > ul,
.stricky-header .main-menu__list > .megamenu.megamenu-clickable > ul {
  position: fixed;
  top: 0 !important;
  left: 0 !important;
  width: 100vw;
  height: 100vh;
  visibility: visible;
  overflow-y: scroll;
  visibility: hidden;
  opacity: 0;
  -webkit-transform: scale(1, 0);
  transform: scale(1, 0);
  -webkit-transform-origin: bottom center;
  transform-origin: bottom center;
  transition: transform 0.7s ease, opacity 0.7s ease, visibility 0.7s ease;
  z-index: 999999;
  -ms-overflow-style: none;
  scrollbar-width: none;
  overflow-y: scroll;
  padding: 0;
  background-color: var(--grdeen-white, #fff);
  display: block !important;
  margin: 0;
}

.main-menu__list > li.megamenu-clickable > ul::-webkit-scrollbar {
  display: none;
}

.mobile-nav__container .main-menu__list > .megamenu.megamenu-clickable > ul.megamenu-clickable--active,
.main-menu .main-menu__list > .megamenu.megamenu-clickable > ul.megamenu-clickable--active,
.stricky-header .main-menu__list > .megamenu.megamenu-clickable > ul.megamenu-clickable--active {
  -webkit-transform-origin: top center;
  transform-origin: top center;
  -webkit-transform: scale(1, 1);
  transform: scale(1, 1);
  opacity: 1;
  visibility: visible;
  transition: transform 0.7s ease, opacity 0.7s ease, visibility 0.7s ease;
}

body.megamenu-popup-active {
  overflow: hidden;
}

body.megamenu-popup-active .stricky-header {
  bottom: 0;
}

body.megamenu-popup-active .mobile-nav__content {
  overflow: unset;
}

.mobile-nav__content .demo-one .container {
  padding-left: 15px;
  padding-right: 15px;
}

.megamenu-popup {
  position: relative;
}
.megamenu-popup .megamenu-clickable--close {
  position: absolute;
  top: 18px;
  right: 20px;
  display: block;
  color: var(--grdeen-black, #172000);
}
@media (min-width: 1300px) {
  .megamenu-popup .megamenu-clickable--close {
    top: 38px;
    right: 40px;
  }
}
.megamenu-popup .megamenu-clickable--close:hover {
  color: var(--grdeen-base, #1a9120);
}
.megamenu-popup .megamenu-clickable--close span {
  width: 24px;
  height: 24px;
  display: block;
  position: relative;
  color: currentColor;
  transition: all 500ms ease;
}
.megamenu-popup .megamenu-clickable--close span::before, .megamenu-popup .megamenu-clickable--close span::after {
  content: "";
  width: 100%;
  height: 2px;
  background-color: currentColor;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
}
.megamenu-popup .megamenu-clickable--close span::after {
  transform: translate(-50%, -50%) rotate(45deg);
}

/*--------------------------------------------------------------
# Home Showcase
--------------------------------------------------------------*/
.demo-one {
  padding-top: 120px;
  padding-bottom: 120px;
}
.demo-one .row {
  --bs-gutter-y: 30px;
}
.demo-one__card {
  background-color: var(--grdeen-white, #fff);
  box-shadow: 0px 10px 60px 0px rgba(var(--grdeen-black3-rgb, 84, 84, 84), 0.1);
  text-align: center;
  transition: 500ms ease;
  transform: translateY(0px);
}
.demo-one__card:hover {
  transform: translateY(-10px);
}
.demo-one__title {
  margin: 0;
  text-transform: capitalize;
  font-size: 16px;
  color: var(--grdeen-black, #172000);
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.demo-one__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.demo-one__title a:hover {
  background-size: 100% 1px;
}
.demo-one__image {
  position: relative;
  overflow: hidden;
}
.demo-one__image img {
  max-width: 100%;
  transition: filter 500ms ease;
  filter: blur(0px);
}
.demo-one__card:hover .demo-one__image img {
  filter: blur(2px);
}
.demo-one__btns {
  background-color: rgba(var(--grdeen-black4-rgb, 13, 25, 14), 0.7);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  transform: scale(1, 0);
  transition: transform 500ms ease, opacity 600ms linear;
  transform-origin: bottom center;
  opacity: 0;
}
.demo-one__card:hover .demo-one__btns {
  transform: scale(1, 1);
  opacity: 1;
  transform-origin: top center;
}
.demo-one__btn {
  font-size: 12px;
  padding: 11px 20px;
  min-width: 125px;
  text-align: center;
  justify-content: center;
}
.demo-one__title {
  padding-top: 20.5px;
  padding-bottom: 20.5px;
}

.home-showcase {
  margin-bottom: -20px;
  margin-left: -50px;
}
.home-showcase .row {
  --bs-gutter-x: 42px;
  --bs-gutter-y: 20px;
}
.home-showcase__inner {
  padding: 40px 40px 21px;
  background-color: var(--grdeen-white, #fff);
  box-shadow: 0px 10px 60px 0px rgba(var(--grdeen-black3-rgb, 84, 84, 84), 0.07);
}
.home-showcase .demo-one__card {
  box-shadow: none;
}
.home-showcase .demo-one__btns {
  flex-direction: column;
}
.home-showcase .demo-one__btn {
  min-width: 125px;
  text-align: center;
  justify-content: center;
}
.home-showcase .demo-one__title {
  padding: 0;
  font-size: 15px;
  margin-top: 15px;
  padding-bottom: 15px;
}

/*--------------------------------------------------------------
# Why choose
--------------------------------------------------------------*/
.why-choose-one {
  position: relative;
}
.why-choose-one__shape-1 {
  display: none;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
  animation: shapeMove 4s linear 0s infinite;
}
@media (min-width: 992px) {
  .why-choose-one__shape-1 {
    display: block;
  }
}
@keyframes shapeMove {
  0%, 100% {
    transform: translateX(0px);
  }
  50% {
    transform: translateX(10px);
  }
}
.why-choose-one__inner {
  background-color: var(--grdeen-gray, #f6f7f2);
  padding-top: 120px;
  padding-bottom: 120px;
  position: relative;
}
.why-choose-one__inner::before {
  content: "";
  width: 10000px;
  height: 100%;
  background-color: var(--grdeen-gray, #f6f7f2);
  position: absolute;
  top: 0;
  right: 100%;
}
.why-choose-one .container {
  position: relative;
}
.why-choose-one__content {
  padding-left: 30px;
  padding-right: 30px;
}
@media (min-width: 1200px) {
  .why-choose-one__content {
    padding-left: 0;
    padding-right: 0;
  }
}
.why-choose-one__image {
  text-align: right;
  margin-top: 35px;
}
@media (min-width: 1200px) {
  .why-choose-one__image {
    margin-top: 0;
  }
}
.why-choose-one__image img {
  max-width: 100%;
}
@media (min-width: 1200px) {
  .why-choose-one__image img {
    max-width: none;
  }
}
.why-choose-one__highlighted {
  margin: 0;
  color: var(--grdeen-base, #1a9120);
  font-size: 18px;
  line-height: 30px;
  margin-top: -20px;
}
@media (min-width: 768px) {
  .why-choose-one__highlighted {
    font-size: 20px;
  }
}
.why-choose-one__text {
  margin: 0;
  font-size: 15px;
  line-height: 2em;
  margin-top: 15px;
}
@media (min-width: 1200px) {
  .why-choose-one__text {
    margin-top: 20px;
  }
}
.why-choose-one__list {
  margin-top: 45px;
}
.why-choose-one__list__item {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
}
@media (min-width: 1200px) {
  .why-choose-one__list__item {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
.why-choose-one__list__item + .why-choose-one__list__item {
  margin-top: 20px;
}
.why-choose-one__list__icon {
  width: 57px;
  height: 57px;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: var(--grdeen-white, #fff);
  font-size: 21px;
  flex-shrink: 0;
  border-radius: 50%;
  margin-bottom: 15px;
  transition: all 500ms ease;
}
.why-choose-one__list__icon i {
  transform: scale(1);
  transition: 500ms ease;
}
.why-choose-one__list__icon:hover {
  background-color: var(--grdeen-black, #172000);
  color: var(--grdeen-white, #fff);
}
.why-choose-one__list__icon:hover i {
  transform: scale(0.9);
}
@media (min-width: 1200px) {
  .why-choose-one__list__icon {
    margin-bottom: 0;
    margin-right: 20px;
  }
}
.why-choose-one__list__title {
  margin: 0;
  text-transform: uppercase;
  font-size: 20px;
  color: var(--grdeen-black, #172000);
  font-weight: bold;
}
@media (min-width: 1200px) {
  .why-choose-one__list__title {
    min-width: 130px;
  }
}
.why-choose-one__list__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.why-choose-one__list__title a:hover {
  background-size: 100% 1px;
}
.why-choose-one__list__text {
  margin: 0;
  font-size: 15px;
  line-height: 26px;
  position: relative;
}
.why-choose-one__list__text::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 1px;
  height: 57px;
  background-color: var(--grdeen-border-color, #e7e7e7);
  top: 50%;
  transform: translateY(-50%);
  display: none;
}
@media (min-width: 1200px) {
  .why-choose-one__list__text::before {
    display: block;
  }
}
@media (min-width: 1200px) {
  .why-choose-one__list__text {
    padding-left: 30px;
  }
}

.why-choose-two {
  padding-top: 100px;
}
.why-choose-two__image {
  position: relative;
  display: inline-block;
  margin-bottom: 110px;
}
@media (min-width: 992px) {
  .why-choose-two__image {
    margin-bottom: 0;
  }
}
@media (min-width: 1200px) {
  .why-choose-two__image {
    margin-left: 120px;
  }
}
.why-choose-two__image img {
  max-width: 100%;
}
.why-choose-two__image__two {
  position: absolute;
  bottom: -50px;
  left: 0px;
  z-index: 10;
}
@media (min-width: 992px) {
  .why-choose-two__image__two {
    bottom: 0;
    left: -120px;
  }
}
.why-choose-two__image__shape {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 11;
  animation: shapeMove 4s linear 0s infinite;
}
@media (min-width: 992px) {
  .why-choose-two__image__shape {
    bottom: auto;
    top: 200px;
    right: auto;
    left: -100px;
  }
}
.why-choose-two__image__icon {
  width: 96px;
  height: 96px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--grdeen-base, #1a9120);
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
}
@media (min-width: 992px) {
  .why-choose-two__image__icon {
    left: -48px;
  }
}
@media (min-width: 1200px) {
  .why-choose-two__content {
    padding-left: 70px;
  }
}
.why-choose-two__highlight {
  font-size: 18px;
  line-height: 30px;
  font-weight: 500;
  color: var(--grdeen-base, #1a9120);
  margin: 0;
  margin-top: -10px;
  margin-bottom: 20px;
}
@media (min-width: 992px) {
  .why-choose-two__highlight {
    margin: 0;
    font-size: 20px;
    line-height: 34px;
    margin-top: -20px;
    margin-bottom: 32px;
  }
}
.why-choose-two__text {
  margin: 0;
  font-size: 15px;
  line-height: 1.875em;
}
@media (min-width: 992px) {
  .why-choose-two__text {
    font-size: 16px;
  }
}
.why-choose-two__progress {
  margin-top: 30px;
  margin-bottom: 37px;
}
.why-choose-two__progress__title {
  text-transform: uppercase;
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}
.why-choose-two__progress__bar {
  width: 100%;
  height: 17px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  box-shadow: inset 0px 0px 7px 0px rgba(0, 0, 0, 0.15);
  position: relative;
}
.why-choose-two__progress__inner {
  position: absolute;
  height: calc(100% - 6px);
  left: 4px;
  top: 3px;
  background-color: var(--grdeen-base, #1a9120);
  transition: all 700ms linear;
  width: 0px;
}
.why-choose-two__progress__number {
  position: absolute;
  bottom: calc(100% + 5px);
  right: 0;
  font-size: 14px;
  font-weight: 400;
}
.why-choose-two__link:hover {
  color: var(--grdeen-white, #fff);
}
.why-choose-two__link::after {
  background-color: var(--grdeen-black, #172000);
}
.why-choose-two__link::before {
  background-color: var(--grdeen-base, #1a9120);
}

.why-choose-three {
  position: relative;
  padding: 120px 0 220px;
  background-color: var(--grdeen-black, #172000);
  margin-bottom: -100px;
}
@media (max-width: 767px) {
  .why-choose-three {
    padding: 80px 0 180px;
  }
}
.why-choose-three__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--grdeen-black, #172000);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  opacity: 0.3;
}
.why-choose-three__content {
  position: relative;
}
.why-choose-three__content__text {
  font-size: 15px;
  line-height: 30px;
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.6);
  margin: 0;
}
.why-choose-three .sec-title__title {
  color: var(--grdeen-white, #fff);
}
.why-choose-three .sec-title {
  padding-bottom: 26px;
}
.why-choose-three__list {
  position: relative;
  background-color: var(--grdeen-black2, #4f5345);
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  row-gap: 0;
}
@media (max-width: 991px) {
  .why-choose-three__list {
    margin: 50px 0 0;
  }
}
.why-choose-three__item {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 40px 40px 36px;
}
.why-choose-three__item:nth-child(1), .why-choose-three__item:nth-child(3) {
  border-right: 1px solid rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}
.why-choose-three__item:nth-child(1), .why-choose-three__item:nth-child(2) {
  border-bottom: 1px solid rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}
@media (min-width: 992px) and (max-width: 1199px) {
  .why-choose-three__item {
    padding-left: 25px;
    padding-right: 25px;
  }
}
@media (max-width: 767px) {
  .why-choose-three__item {
    flex: 0 0 100%;
    max-width: 100%;
    border-bottom: 1px solid rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
  }
}
.why-choose-three__item:hover .why-choose-three__item__icon span {
  transform: scale(0.8);
}
.why-choose-three__item__top {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 12px;
}
.why-choose-three__item__icon {
  width: 57px;
  height: 57px;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--grdeen-white, #fff);
  font-size: 21px;
  margin-right: 18px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .why-choose-three__item__icon {
    margin-right: 12px;
  }
}
.why-choose-three__item__icon span {
  display: inline-block;
  transition: all 500ms linear;
  transition-delay: 0s;
  transition-delay: 0s;
  transition-delay: 0.1s;
  transform: scale(1);
}
.why-choose-three__item__title {
  color: var(--grdeen-white, #fff);
  font-size: 20px;
  text-transform: uppercase;
  font-weight: 700;
  margin: 0;
}
.why-choose-three__item__text {
  color: var(--grdeen-text-dark, #07370a);
  font-size: 15px;
  line-height: 30px;
  margin: 0;
}

/*--------------------------------------------------------------
# Funfact
--------------------------------------------------------------*/
.funfact-one {
  padding: 120px 0;
  padding-bottom: 112px;
}
@media (max-width: 767px) {
  .funfact-one {
    padding: 80px 0;
  }
}
@media (max-width: 1200px) {
  .funfact-one .container {
    max-width: 100%;
  }
}
.funfact-one__content {
  margin-top: -5px;
  padding-right: 34px;
}
@media (max-width: 991px) {
  .funfact-one__content {
    padding-right: 0;
  }
}
.funfact-one__content .sec-title {
  padding-bottom: 22px;
}
.funfact-one__content .sec-title__title {
  margin-top: 6px;
}
.funfact-one__content__text {
  font-weight: 400;
  font-size: 16px;
  line-height: 30px;
  margin-bottom: 37px;
}
.funfact-one__content__bookwrap {
  background-color: var(--grdeen-white2, #f2f4ec);
}
@media (max-width: 767px) {
  .funfact-one__content__bookwrap {
    flex-direction: column;
    gap: 10px 0;
    padding: 15px;
  }
}
.funfact-one__content__bookwrap__btn {
  background-color: var(--grdeen-text-dark, #07370a);
  padding: 16.5px 18px;
  border-radius: 0;
}
.funfact-one__content__bookwrap__btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.funfact-one__content__bookwrap__text {
  font-weight: 500;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 16px;
  line-height: 30px;
  margin-bottom: 0;
  padding-left: 24px;
}
@media (max-width: 767px) {
  .funfact-one__content__bookwrap__text {
    text-align: center;
    padding-left: 0;
  }
}
.funfact-one__list {
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0 30px;
}
@media (max-width: 767px) {
  .funfact-one__list {
    gap: 30px 0;
  }
}
.funfact-one__item {
  width: calc((100% - 30px) / 2);
}
.funfact-one__item .work-process-one__col {
  padding: 36px 23px 32px 23px;
}
.funfact-one__item .work-process-one__col:hover .work-process-one__col__icon {
  background-color: var(--grdeen-text-dark, #07370a);
  box-shadow: 0px 0px 0px 9px rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.8);
}
.funfact-one__item .work-process-one__col__circlewrap::before {
  display: none;
}
.funfact-one__item .work-process-one__col__icon {
  font-size: initial;
  background-color: var(--grdeen-base, #1a9120);
}
@media (max-width: 767px) {
  .funfact-one__item {
    width: calc((100% - 0px) / 1);
  }
}
.funfact-one__countwrap {
  display: flex;
}
.funfact-one__count {
  color: var(--grdeen-white, #fff);
  font-weight: 700;
  font-size: 30px;
  line-height: 1;
}
.funfact-one--two {
  padding-top: 0;
  padding-bottom: 120px;
  background-color: #136d17;
  position: relative;
}
@media (max-width: 767px) {
  .funfact-one--two {
    padding-bottom: 80px;
  }
}
.funfact-one--two .funfact-one__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}
.funfact-one--two .funfact-one__wrapper {
  max-width: 1570px;
  margin: 0 auto;
  background-color: var(--grdeen-white2, #f2f4ec);
  padding-top: 120px;
  padding-bottom: 120px;
  position: relative;
  z-index: 2;
  overflow: hidden;
}
@media (max-width: 991px) {
  .funfact-one--two .funfact-one__wrapper {
    padding-top: 80px;
    padding-bottom: 80px;
  }
}
.funfact-one--two .funfact-one__wrapper__shapeleft, .funfact-one--two .funfact-one__wrapper__shaperight {
  position: absolute;
  width: 100%;
  z-index: 2;
  bottom: -5px;
  display: flex;
}
.funfact-one--two .funfact-one__wrapper__shapeleft {
  left: -4px;
  justify-content: flex-start;
}
.funfact-one--two .funfact-one__wrapper__shaperight {
  right: -4px;
  justify-content: flex-end;
}
.funfact-one--two .container {
  position: relative;
  z-index: 3;
}
.funfact-one--two .funfact-one__content__bookwrap,
.funfact-one--two .work-process-one__col {
  background-color: var(--grdeen-white, #fff);
}
.funfact-one--two .work-process-one__col__shapebg {
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.07);
  left: 1px;
  bottom: -1px;
}
.funfact-one--two .work-process-one__col__circlewrap {
  position: relative;
}
.funfact-one--two .work-process-one__col__shapetree {
  position: absolute;
  width: 65px;
  height: auto;
  top: -32px;
  right: 0;
  left: 0;
  margin: 0 auto;
  z-index: 1;
  animation: funfactEffect2 1.5s linear infinite alternate;
}
@keyframes funfactEffect2 {
  0% {
    transform: translateX(-9px);
  }
  100% {
    transform: translateX(9px);
  }
}
.funfact-one--three {
  position: relative;
  padding-top: 395px;
  margin-top: -304px;
}
@media (max-width: 767px) {
  .funfact-one--three {
    padding-top: 260px;
    margin-top: -200px;
  }
}
.funfact-one--three .funfact-one__bg {
  background-position: center bottom;
  background-repeat: no-repeat;
}
.funfact-one--about {
  position: relative;
  padding-top: 0;
}

/*--------------------------------------------------------------
# Testimonials
--------------------------------------------------------------*/
.testimonials-one {
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .testimonials-one {
    padding-bottom: 80px;
  }
}
@media (max-width: 1200px) {
  .testimonials-one .container {
    max-width: 100%;
  }
}
.testimonials-one .sec-title {
  text-align: center;
}
.testimonials-one .sec-title__title {
  margin-top: 0;
}
.testimonials-one .row {
  --bs-gutter-x: 15px;
}
.testimonials-one__carousel .owl-nav.disabled {
  display: none !important;
}
.testimonials-one--two {
  padding-top: 112px;
}
@media (max-width: 767px) {
  .testimonials-one--two {
    padding-top: 74px;
    padding-bottom: 80px;
  }
}
.testimonials-one--two .testimonials-card {
  padding: 0;
  box-shadow: none;
}
.testimonials-one--two .testimonials-card__inner {
  border: 3px solid var(--grdeen-base, #1a9120);
  background-color: var(--grdeen-text-dark2, #0e150e);
  border-radius: 8px;
  padding: 37px 30px 33px 35px;
  margin-top: 6px;
  position: relative;
}
.testimonials-one--two .testimonials-card__inner::after {
  position: absolute;
  left: 6px;
  top: -6px;
  width: 91%;
  height: 6px;
  content: "";
  background-color: var(--grdeen-base, #1a9120);
  border-radius: 8px 8px 0 0;
}
.testimonials-one--two .testimonials-card__rating__start {
  font-size: 14px;
  letter-spacing: 4px;
}
.testimonials-one--two .testimonials-card__content {
  padding: 0;
  margin-bottom: 29px;
  color: #6c816c;
}
.testimonials-one--two .testimonials-card__top {
  margin: 0;
}
.testimonials-one--two .testimonials-card__image {
  width: 72px;
  min-width: 72px;
  height: 72px;
  border-radius: 50%;
  border: 0;
  -webkit-box-shadow: 0px 0px 0px 4px var(--grdeen-white, #fff);
  box-shadow: 0px 0px 0px 4px var(--grdeen-white, #fff);
  margin-right: 0;
}
.testimonials-one--two .testimonials-card__image::after {
  content: none;
}
.testimonials-one--two .testimonials-card__image img {
  width: 100% !important;
  height: auto;
}
.testimonials-one--two .testimonials-card__top__left {
  width: calc((100% - 72px) / 1);
  padding-left: 22px;
  padding-right: 35px;
}
.testimonials-one--two .testimonials-card__name {
  margin: 0 0 1px;
  color: var(--grdeen-white, #fff);
}
.testimonials-one--two .testimonials-card__designation {
  color: #7b857a;
}
.testimonials-one--two .testimonials-card__quote {
  background-color: transparent;
  right: 0px;
  top: 7px;
  width: auto;
  height: auto;
  display: block;
}

.testimonials-card {
  transition: all 500ms ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0px 0px 33px -5px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  padding-bottom: 34px;
}
.testimonials-card__masking {
  position: absolute;
  width: 100%;
  height: 100%;
  max-height: 170px;
  left: 0;
  bottom: 0;
  z-index: 1;
  -webkit-mask: url('data:image/svg+xml;utf8,<svg version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M0 0 C17.03390441 0 17.03390441 0 24.5546875 0.40234375 C26.35826943 0.48951644 28.16190239 0.57563769 29.96557617 0.66088867 C31.39430557 0.72885147 31.39430557 0.72885147 32.85189819 0.79818726 C88.18938196 3.34828884 143.62113348 3.19041849 199 2.625 C200.04969845 2.6143539 201.0993969 2.60370781 202.18090439 2.59273911 C222.16171179 2.38753025 242.1417505 2.14778864 262.11962891 1.74389648 C299.72703805 0.99038584 337.32029078 0.81196598 374.93391919 0.81488919 C381.59172952 0.81453697 388.24948744 0.80287795 394.90728128 0.7889536 C401.49491358 0.77569622 408.08252306 0.77052035 414.67016852 0.77115268 C418.53423177 0.77140099 422.39823039 0.76893405 426.26227951 0.75797462 C453.93844978 0.68481088 481.68612149 1.49041782 509.1875 4.8125 C509.89607147 4.89614807 510.60464294 4.97979614 511.33468628 5.065979 C530.83821401 7.38581867 552.19905853 10.09952927 570 19 C570 68.5 570 118 570 169 C381.9 169 193.8 169 0 169 C0 113.23 0 57.46 0 0 Z " transform="translate(0,1)"/></svg>');
  mask: url('data:image/svg+xml;utf8,<svg version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M0 0 C17.03390441 0 17.03390441 0 24.5546875 0.40234375 C26.35826943 0.48951644 28.16190239 0.57563769 29.96557617 0.66088867 C31.39430557 0.72885147 31.39430557 0.72885147 32.85189819 0.79818726 C88.18938196 3.34828884 143.62113348 3.19041849 199 2.625 C200.04969845 2.6143539 201.0993969 2.60370781 202.18090439 2.59273911 C222.16171179 2.38753025 242.1417505 2.14778864 262.11962891 1.74389648 C299.72703805 0.99038584 337.32029078 0.81196598 374.93391919 0.81488919 C381.59172952 0.81453697 388.24948744 0.80287795 394.90728128 0.7889536 C401.49491358 0.77569622 408.08252306 0.77052035 414.67016852 0.77115268 C418.53423177 0.77140099 422.39823039 0.76893405 426.26227951 0.75797462 C453.93844978 0.68481088 481.68612149 1.49041782 509.1875 4.8125 C509.89607147 4.89614807 510.60464294 4.97979614 511.33468628 5.065979 C530.83821401 7.38581867 552.19905853 10.09952927 570 19 C570 68.5 570 118 570 169 C381.9 169 193.8 169 0 169 C0 113.23 0 57.46 0 0 Z " transform="translate(0,1)"/></svg>');
  -webkit-mask-repeat: repeat;
  mask-repeat: repeat;
  -webkit-mask-position: center bottom;
  mask-position: center bottom;
  -webkit-mask-size: cover;
  mask-size: cover;
  background-color: var(--grdeen-white, #fff);
  background-position: left bottom;
  background-repeat: no-repeat;
  background-size: cover;
}
.testimonials-card__inner {
  background-color: rgba(var(--grdeen-white2-rgb, 242, 244, 236), 0.9);
}
.testimonials-card__top {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  margin-bottom: -12px;
}
@media (max-width: 991px) {
  .testimonials-card__top {
    margin-bottom: -5px;
  }
}
.testimonials-card__image {
  flex-shrink: 0;
  margin-right: 22px;
  border: 2px dashed var(--grdeen-base, #1a9120);
  border-radius: 8% 50% 50% 0;
  transition: all 500ms ease;
  width: 138px;
  min-width: 138px;
  height: 138px;
  background-color: var(--grdeen-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.testimonials-card__image::after {
  content: "";
  position: absolute;
  width: calc(100% + 2px);
  height: calc(100% + 4px);
  top: -2px;
  left: -8px;
  z-index: 1;
  border-radius: 0 55% 55% 0;
  background-color: var(--grdeen-white, #fff);
  transition: all 500ms ease;
}
.testimonials-card__image img {
  transition: all 500ms ease;
  position: relative;
  z-index: 2;
  width: 106px !important;
  height: 106px;
  border-radius: 50%;
}
.testimonials-card:hover .testimonials-card__image img {
  -webkit-box-shadow: 0px 0px 1px 8px rgba(26, 145, 32, 0.7);
  box-shadow: 0px 0px 1px 8px rgba(26, 145, 32, 0.7);
}
.testimonials-card__rating {
  display: flex;
  align-items: center;
}
.testimonials-card__rating__start {
  color: var(--grdeen-base, #1a9120);
  font-size: 12px;
}
.testimonials-card__rating__start svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
.testimonials-card__top__left {
  position: relative;
}
.testimonials-card__name {
  margin: 0;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 22px;
  line-height: 26px;
  font-weight: 600;
  margin-top: 13px;
  margin-bottom: 1px;
}
.testimonials-card__designation {
  margin: 0;
  line-height: 1;
  font-size: 14px;
  font-weight: 400;
}
.testimonials-card__quote {
  position: absolute;
  right: 40px;
  top: 32px;
  width: 70px;
  height: 70px;
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  color: var(--grdeen-base, #1a9120);
  font-size: 28px;
}
.testimonials-card__contentwrap {
  position: relative;
  padding-top: 40px;
}
.testimonials-card__content {
  padding: 0 40px;
  font-size: 16px;
  line-height: 30px;
  position: relative;
  z-index: 2;
}
.testimonials-card__ratingwrap {
  margin-bottom: 25px;
}
.testimonials-card__icon {
  width: 47px;
  animation: testimonialEffect2 1.3s linear infinite alternate;
}
@keyframes testimonialEffect2 {
  0% {
    transform: rotate(-12deg);
  }
  100% {
    transform: rotate(12deg);
  }
}
.testimonials-card__icon img {
  width: 100% !important;
}
.testimonials-card__shape2 {
  position: absolute;
  width: 115px;
  height: auto;
  left: 0;
  bottom: 0;
  z-index: 1;
  transition: all 0.5s ease;
}
.testimonials-card__shape2 img {
  transition: all 0.5s ease;
  width: 100% !important;
}
.testimonials-card:hover .testimonials-card__shape2 img {
  filter: brightness(0) invert(1) drop-shadow(2px 4px 6px var(--grdeen-base, #1a9120));
}

.testimonials-two {
  position: relative;
  padding: 120px 0;
  background-color: var(--grdeen-black, #172000);
}
@media (max-width: 767px) {
  .testimonials-two {
    padding: 80px 0;
  }
}
.testimonials-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--grdeen-black, #172000);
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  opacity: 0.3;
}
.testimonials-two__carousel {
  position: relative;
  text-align: center;
  max-width: 850px;
  margin: auto;
}
.testimonials-two__item {
  position: relative;
  margin: 0 0 22px;
}
.testimonials-two__item__ratings {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--grdeen-base, #1a9120);
  font-size: 14px;
  letter-spacing: 3px;
  margin-bottom: 25px;
}
.testimonials-two__item__quote {
  font-size: 36px;
  line-height: 50px;
  font-weight: 500;
  color: var(--grdeen-white, #fff);
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-style: italic;
}
.testimonials-two__carousel-thumb {
  max-width: 525px;
  margin: auto;
  position: relative;
}
.testimonials-two__carousel-thumb .item {
  position: relative;
  text-align: center;
  display: inline-block;
  padding: 30px 0 0;
}
.testimonials-two__carousel-thumb .item .testimonials-two__meta-thumb {
  width: 72px;
  height: 72px;
  border-radius: 50%;
  object-fit: cover;
  display: block;
  position: relative;
  margin-bottom: 22px;
}
.testimonials-two__carousel-thumb .item .testimonials-two__meta-thumb::after {
  position: absolute;
  left: -7px;
  top: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  margin: auto;
  width: calc(100% + 14px);
  height: calc(100% + 14px);
  content: "";
  border: 2px solid var(--grdeen-base, #1a9120);
  visibility: hidden;
  opacity: 0;
  transition: 500ms ease;
  transform: scale(0.8);
}
.testimonials-two__carousel-thumb .item .testimonials-two__meta-thumb img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}
.testimonials-two__carousel-thumb .active.center .item .testimonials-two__meta-thumb::after {
  visibility: visible;
  opacity: 1;
  transform: scale(1.1);
}
.testimonials-two__carousel-thumb .active.center .testimonials-two__meta {
  visibility: visible;
  opacity: 1;
  transform: translateY(0);
}
.testimonials-two__meta {
  position: relative;
  display: block;
  visibility: hidden;
  margin-left: -48%;
  opacity: 0;
  transform: translateY(20%);
  transition: 500ms ease;
}
.testimonials-two__meta__name {
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  line-height: 1;
  margin-bottom: 8px;
  color: var(--grdeen-white, #fff);
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
}
.testimonials-two__meta__designation {
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  font-size: 12px;
  line-height: 1;
  font-weight: 500;
  text-transform: uppercase;
  line-height: 1;
  letter-spacing: 2.4px;
  color: var(--grdeen-base, #1a9120);
}

.testimonials-three {
  position: relative;
  background-color: var(--grdeen-gray, #f6f7f2);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .testimonials-three {
    padding: 80px 0;
  }
}
.testimonials-three__content {
  position: relative;
}
.testimonials-three__content .sec-title {
  padding-bottom: 27px;
}
.testimonials-three__content__text {
  font-size: 15px;
  line-height: 30px;
  margin: 0 0 28px;
}
.testimonials-three__carousel-nav {
  position: relative;
  display: flex;
  align-items: center;
}
.testimonials-three__carousel-nav a {
  width: 57px;
  height: 57px;
  background-color: var(--grdeen-white, #fff);
  font-size: 16px;
  color: var(--grdeen-black, #172000);
  border-radius: 50%;
  line-height: 58px;
  text-align: center;
}
.testimonials-three__carousel-nav a:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.testimonials-three__carousel-nav a + a {
  margin-left: 10px;
}
.testimonials-three__item {
  position: relative;
  display: flex;
}
@media (max-width: 1199px) {
  .testimonials-three__item {
    margin-top: 50px;
  }
}
@media (max-width: 767px) {
  .testimonials-three__item {
    display: block;
  }
}
.testimonials-three__item__content {
  max-width: 430px;
  position: relative;
  z-index: 2;
  background-color: var(--grdeen-white, #fff);
  background-position: top right;
  background-repeat: no-repeat;
  padding: 10px;
  box-shadow: 0px 10px 60px 0px rgba(var(--grdeen-black3-rgb, 84, 84, 84), 0.07);
}
.testimonials-three__item__content::after {
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 74px 40px 0;
  border-color: transparent var(--grdeen-white, #fff) transparent transparent;
  position: absolute;
  right: 115px;
  bottom: -40px;
  content: "";
}
.testimonials-three__item__thumb {
  position: absolute;
  right: 0;
  top: 0;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .testimonials-three__item__thumb {
    right: 50px;
  }
}
@media (max-width: 767px) {
  .testimonials-three__item__thumb {
    position: relative;
    margin: 50px 0 0;
  }
}
.testimonials-three__item__thumb-one {
  display: inline-block;
  border-radius: 50%;
  overflow: hidden;
  margin: 26px 0 0;
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.testimonials-three__item__thumb-one img {
  max-width: 100%;
  border-radius: 50%;
}
.testimonials-three__item__thumb-two {
  position: absolute;
  right: 0;
  top: 0;
  width: 197px;
  height: 197px;
  border-radius: 50%;
  overflow: hidden;
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
.testimonials-three__item__thumb-two img {
  max-width: 100%;
  border-radius: 50%;
}
.testimonials-three__item__thumb-flower {
  position: absolute;
  right: -53px;
  top: 68px;
  -webkit-animation-duration: 0.5s;
  animation-duration: 0.5s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}
@media (max-width: 767px) {
  .testimonials-three__item__thumb-flower {
    display: none;
  }
}
.testimonials-three__item__thumb-flower img {
  max-width: 100%;
  -webkit-animation-name: float-bob-y-2;
  animation-name: float-bob-y-2;
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}
.testimonials-three__meta {
  display: flex;
  align-items: center;
  background-color: var(--grdeen-base, #1a9120);
  padding: 10px 48px 10px;
  margin-bottom: 29px;
}
@media (max-width: 767px) {
  .testimonials-three__meta {
    padding-right: 30px;
    padding-left: 30px;
  }
}
.testimonials-three__meta__title {
  margin: 0;
  font-size: 18px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--grdeen-white, #fff);
}
.testimonials-three__meta__designation {
  position: relative;
  margin: 0 0 0 30px;
  font-size: 14px;
  color: var(--grdeen-white, #fff);
}
.testimonials-three__meta__designation::before {
  position: absolute;
  left: -18px;
  bottom: -3px;
  content: ".";
  color: var(--grdeen-white, #fff);
  font-size: 18px;
  font-weight: 700;
}
.testimonials-three__ratings {
  display: flex;
  align-items: center;
  color: var(--grdeen-base, #1a9120);
  font-size: 16px;
  letter-spacing: 3px;
  margin: 0 0px 19px 50px;
}
@media (max-width: 767px) {
  .testimonials-three__ratings {
    margin-left: 30px;
  }
}
.testimonials-three__quote {
  font-size: 24px;
  line-height: 40px;
  color: var(--grdeen-black, #172000);
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-weight: 700;
  font-style: italic;
  margin: 0 40px 36px 50px;
}
@media (max-width: 767px) {
  .testimonials-three__quote {
    margin-right: 30px;
    margin-left: 30px;
  }
}
.testimonials-three .active .testimonials-three__item__thumb-one {
  animation-delay: 0.3s;
  animation-name: fadeInUp;
}
.testimonials-three .active .testimonials-three__item__thumb-two {
  animation-delay: 0.4s;
  animation-name: fadeInUp;
}
.testimonials-three .active .testimonials-three__item__thumb-flower {
  animation-delay: 0.5s;
  animation-name: fadeInUp;
}

/*--------------------------------------------------------------
# CTA
--------------------------------------------------------------*/
.cta-one {
  padding-bottom: 120px;
  position: relative;
}
.cta-one .container-fluid {
  max-width: 1380px;
}
.cta-one__inner {
  position: relative;
  padding: 20px;
}
.cta-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.cta-one__title {
  margin: 0;
  text-transform: uppercase;
  max-width: 513px;
  width: 100%;
  font-size: 35px;
  line-height: 1.2em;
  font-weight: bold;
  margin-bottom: 15px;
}
.cta-one__title span {
  font-family: var(--grdeen-special-font, "Alex Brush", cursive);
  font-weight: 400;
}
@media (min-width: 992px) {
  .cta-one__title {
    font-size: 50px;
    margin-bottom: 30px;
    margin-top: -10px;
  }
}
.cta-one__content {
  position: relative;
  border: 1px solid var(--grdeen-white, #fff);
  padding: 50px 20px;
}
@media (min-width: 768px) {
  .cta-one__content {
    padding: 60px;
  }
}
@media (min-width: 1200px) {
  .cta-one__content {
    padding: 100px;
  }
}
.cta-one__link:hover {
  color: var(--grdeen-white, #fff);
}
.cta-one__link::after {
  background-color: var(--grdeen-black, #172000);
}
.cta-one__link::before {
  background-color: var(--grdeen-base, #1a9120);
}

.cta-two {
  position: relative;
  background-color: var(--grdeen-base, #1a9120);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
}
.cta-two__shape {
  position: absolute;
  left: -10px;
  top: 0;
}
.cta-two__shape img {
  animation: shapeMove 3s linear 0s infinite;
}
.cta-two__content {
  position: relative;
  padding: 85px 0 90px;
}
.cta-two__sub-title {
  font-family: var(--grdeen-special-font, "Alex Brush", cursive);
  color: var(--grdeen-white, #fff);
  font-size: 40px;
  line-height: 1.2em;
  margin: 0 0 2px;
}
.cta-two__title {
  color: var(--grdeen-white, #fff);
  font-size: 50px;
  text-transform: uppercase;
  font-weight: 700;
  margin: 0 0 37px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .cta-two__title {
    font-size: 44px;
  }
}
@media (max-width: 767px) {
  .cta-two__title {
    font-size: 35px;
  }
}
.cta-two__thumb {
  position: relative;
}
.cta-two__thumb__one {
  position: relative;
  right: 35px;
  margin-top: -28px;
}
@media (max-width: 991px) {
  .cta-two__thumb__one {
    right: 0;
  }
}
.cta-two__thumb__one__shape {
  position: absolute;
  left: -40px;
  top: -40px;
  width: 545px;
  height: 408px;
}
.cta-two__thumb__one__thumb {
  width: 482px;
  height: auto;
  border: 20px solid var(--grdeen-white, #fff);
  position: relative;
  z-index: 2;
  transform: rotate(5deg);
}
@media (max-width: 767px) {
  .cta-two__thumb__one__thumb {
    width: 100%;
  }
}
.cta-two__thumb__one__thumb img {
  width: 100%;
  height: 282px;
  object-fit: cover;
}
@media (max-width: 767px) {
  .cta-two__thumb__one__thumb img {
    height: auto;
  }
}
.cta-two__thumb__two {
  position: absolute;
  right: -48px;
  top: 195px;
  z-index: 3;
}
@media (max-width: 991px) {
  .cta-two__thumb__two {
    top: 45px;
    right: 0;
  }
}
@media (max-width: 767px) {
  .cta-two__thumb__two {
    position: relative;
    top: 0;
    right: 0;
  }
}
.cta-two__thumb__two__flower {
  position: absolute;
  right: -10px;
  top: -121px;
  z-index: 4;
}
@media (max-width: 767px) {
  .cta-two__thumb__two__flower {
    display: none;
  }
}
.cta-two__thumb__two__flower img {
  -webkit-animation-name: float-bob-y-2;
  animation-name: float-bob-y-2;
  -webkit-animation-duration: 4s;
  animation-duration: 4s;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
}
.cta-two__thumb__two__shape {
  position: absolute;
  left: -45px;
  top: -60px;
  width: 446px;
  height: 421px;
}
.cta-two__thumb__two__thumb {
  width: 360px;
  height: auto;
  border: 20px solid var(--grdeen-white, #fff);
  position: relative;
  z-index: 2;
  transform: rotate(-17.2deg);
}
@media (max-width: 767px) {
  .cta-two__thumb__two__thumb {
    width: 100%;
  }
}
.cta-two__thumb__two__thumb img {
  width: 100%;
  height: 215px;
  object-fit: cover;
}
@media (max-width: 767px) {
  .cta-two__thumb__two__thumb img {
    height: auto;
  }
}

/*--------------------------------------------------------------
# Gallery
--------------------------------------------------------------*/
.gallery-one {
  padding-top: 120px;
  padding-bottom: 120px;
}
.gallery-one .container-fluid {
  width: 100%;
  max-width: 1572px;
}
.gallery-one--page {
  padding-top: 100px;
}
.gallery-one .row {
  --bs-gutter-x: 10px;
  --bs-gutter-y: 10px;
}
@media (min-width: 992px) {
  .gallery-one__carousel .owl-nav {
    display: none;
  }
}
.gallery-one__filter__list {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}
.gallery-one__filter__list li {
  cursor: pointer;
}
.gallery-one__filter__list li span {
  display: block;
  font-size: 10px;
  background-color: var(--grdeen-gray, #f6f7f2);
  transition: all 500ms ease;
  text-transform: uppercase;
  font-weight: 600;
  padding: 15px 20px;
  line-height: 1.2em;
}
.gallery-one__filter__list li.active span, .gallery-one__filter__list li:hover span {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.gallery-one__card {
  position: relative;
  overflow: hidden;
  background-color: var(--grdeen-black, #172000);
}
.gallery-one__card img {
  transform: scale(1);
  max-width: 100%;
  transition: transform 500ms ease, opacity 500ms ease;
  opacity: 1;
}
.gallery-one__card__hover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(1, 0);
  transition: transform 500ms ease;
  transform-origin: bottom center;
}
.gallery-one__card__hover .img-popup {
  position: relative;
}
.gallery-one__card:hover img {
  transform: scale(1.05);
  opacity: 0.9;
  mix-blend-mode: screen;
}
.gallery-one__card:hover .gallery-one__card__hover {
  transform-origin: top center;
  transform: scale(1, 1);
}
.gallery-one__card__icon {
  width: 32px;
  height: 32px;
  display: block;
  position: relative;
}
.gallery-one__card__icon::after, .gallery-one__card__icon::before {
  content: "";
  width: 2px;
  height: 100%;
  background-color: var(--grdeen-white, #fff);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.gallery-one__card__icon::after {
  transform: translate(-50%, -50%) rotate(90deg);
}

.gallery-two {
  position: relative;
  overflow: hidden;
  padding: 0 0 20px;
}
.gallery-two .container-fluid {
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
@media (max-width: 767px) {
  .gallery-two .container-fluid {
    padding-left: 15px;
    padding-right: 15px;
  }
}
.gallery-two .row {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}
.gallery-two__col-one {
  width: 65%;
}
@media (max-width: 1199px) {
  .gallery-two__col-one {
    width: 100%;
  }
}
.gallery-two__col-two {
  width: 35%;
}
@media (max-width: 1199px) {
  .gallery-two__col-two {
    width: 100%;
  }
}
.gallery-two__card {
  position: relative;
  overflow: hidden;
  background-color: var(--grdeen-black, #172000);
}
.gallery-two__card img {
  transform: scale(1);
  width: 100%;
  transition: transform 500ms ease, opacity 500ms ease;
  opacity: 1;
}
.gallery-two__card__hover {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  transform: scale(1, 0);
  transition: transform 500ms ease;
  transform-origin: bottom center;
}
.gallery-two__card__hover .img-popup {
  position: relative;
}
.gallery-two__card:hover img {
  transform: scale(1.05);
  opacity: 0.9;
  mix-blend-mode: screen;
}
.gallery-two__card:hover .gallery-two__card__hover {
  transform-origin: top center;
  transform: scale(1, 1);
}
.gallery-two__card a {
  width: 75px;
  height: 75px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gallery-two__card a:hover .gallery-two__card__icon::after, .gallery-two__card a:hover .gallery-two__card__icon::before {
  background-color: var(--grdeen-base, #1a9120);
}
.gallery-two__card__icon {
  width: 24px;
  height: 24px;
  display: block;
  position: relative;
}
.gallery-two__card__icon::after, .gallery-two__card__icon::before {
  content: "";
  width: 2px;
  height: 100%;
  background-color: var(--grdeen-black, #172000);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 400ms ease;
}
.gallery-two__card__icon::after {
  transform: translate(-50%, -50%) rotate(90deg);
}
.gallery-two__info {
  position: relative;
  background-color: var(--grdeen-base, #1a9120);
  background-position: left bottom;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 0 50px 0 100px;
  height: 100%;
}
@media (min-width: 1200px) {
  .gallery-two__info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    flex-direction: column;
  }
}
@media (max-width: 1500px) {
  .gallery-two__info {
    padding-left: 40px;
    padding-right: 30px;
  }
}
@media (max-width: 1199px) {
  .gallery-two__info {
    padding: 80px 50px;
  }
}
@media (max-width: 767px) {
  .gallery-two__info {
    padding: 50px 30px;
  }
}
.gallery-two__info__icon {
  width: 60px;
  height: 60px;
  margin-bottom: 24px;
}
.gallery-two__info__icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.gallery-two__info__title {
  color: var(--grdeen-white, #fff);
  font-size: 44px;
  line-height: 50px;
  text-transform: uppercase;
  font-weight: 700;
  margin: 0;
}
@media (min-width: 1200px) and (max-width: 1300px) {
  .gallery-two__info__title {
    font-size: 35px;
    line-height: 42px;
  }
}
@media (max-width: 767px) {
  .gallery-two__info__title {
    font-size: 35px;
    line-height: 45px;
  }
}

/*--------------------------------------------------------------
# Sidebar
--------------------------------------------------------------*/
.sidebar__single {
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 10px;
  margin-bottom: -1px;
  padding: 33px 40px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .sidebar__single {
    padding-left: 22px;
    padding-right: 20px;
  }
}
.sidebar__title {
  text-transform: capitalize;
  display: inline-block;
  margin: 0;
  line-height: 1em;
  font-size: 20px;
  font-weight: 700;
  border-bottom: 1px solid currentColor;
  margin-top: 4px;
  margin-bottom: 30px;
}
.sidebar__search {
  position: relative;
}
.sidebar__search input[type=search],
.sidebar__search input[type=text] {
  outline: none;
  width: 100%;
  height: 57px;
  background-color: var(--grdeen-white, #fff);
  font-size: 14px;
  font-weight: 500;
  color: var(--grdeen-text, #626f62);
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  padding-left: 22px;
  padding-right: 65px;
  border-radius: 30px 0 0 30px;
  transition: all 500ms ease;
}
.sidebar__search button[type=submit] {
  width: 55px;
  height: 55px;
  border: none;
  outline: none;
  background-color: var(--grdeen-base, #1a9120);
  position: absolute;
  top: 1px;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid var(--grdeen-border-color, #e7e7e7);
  font-size: 16px;
  color: var(--grdeen-white, #fff);
  transition: all 500ms ease;
}
.sidebar__search button[type=submit]:hover {
  background-color: var(--grdeen-black, #172000);
  border-color: var(--grdeen-black, #172000);
  color: var(--grdeen-white, #fff);
}
.sidebar__categories {
  margin-top: -2px;
}
.sidebar__categories li {
  position: relative;
}
.sidebar__categories li a {
  font-size: 17px;
  color: var(--grdeen-text, #626f62);
  display: inline-block;
  font-weight: 500;
  transition: all 500ms ease;
  padding: 0 0;
  position: relative;
}
.sidebar__categories li a::after {
  content: "";
  width: 0;
  height: 1px;
  position: absolute;
  left: auto;
  right: 0;
  bottom: 5px;
  background: var(--grdeen-base, #1a9120);
  transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1) 0s;
}
.sidebar__categories li a span {
  margin-left: 8px;
  display: inline-block;
}
.sidebar__categories li a:hover {
  color: var(--grdeen-base, #1a9120);
}
.sidebar__categories li a:hover::after {
  width: 100%;
  left: 0;
  right: auto;
}
.sidebar__categories li + li {
  margin-top: 16px;
}
.sidebar__posts {
  margin-bottom: 0;
  margin-top: -5px;
}
.sidebar__posts__item {
  display: flex;
  align-items: center;
}
.sidebar__posts__item:not(:last-of-type) {
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  margin-bottom: 15px;
  padding-bottom: 25px;
}
.sidebar__posts__image {
  flex-shrink: 0;
  margin-right: 16px;
}
.sidebar__posts__image img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
}
.sidebar__posts__title {
  margin: 0 0 10px;
  font-size: 18px;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  line-height: 28px;
}
.sidebar__posts__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.sidebar__posts__title a:hover {
  background-size: 100% 1px;
}
.sidebar__posts__meta {
  margin: 0;
  line-height: 1em;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  font-size: 13px;
  font-weight: 500;
}
.sidebar__tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}
.sidebar__tags a {
  background-color: transparent;
  text-transform: capitalize;
  font-size: 13px;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
  transition: all 500ms ease;
  display: inline-flex;
  padding: 4px 16px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 5px;
}
.sidebar__tags a:hover {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
  border-color: var(--grdeen-base, #1a9120);
}
.sidebar__comments {
  margin: 0;
  padding: 0;
}
.sidebar__comments__item {
  display: flex;
  align-items: center;
}
.sidebar__comments__item:not(:last-of-type) {
  margin-bottom: 26px;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  padding-bottom: 20px;
}
.sidebar__comments__icon {
  flex-shrink: 0;
  width: 62px;
  height: 62px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--grdeen-border-color, #e7e7e7);
  font-size: 24px;
  color: var(--grdeen-black3, #545454);
  margin-right: 16px;
  border-radius: 50%;
  transition: all 500ms ease;
}
.sidebar__comments__item:hover .sidebar__comments__icon {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.sidebar__comments__title {
  margin: 0;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 16px;
  line-height: 26px;
  font-weight: 500;
  color: var(--grdeen-text, #626f62);
}
.sidebar__comments__title a {
  color: var(--grdeen-base, #1a9120);
  display: block;
  transition: all 500ms ease;
}
.sidebar__comments__title a:hover {
  color: var(--grdeen-black, #172000);
}

.service-sidebar {
  position: relative;
}
.service-sidebar__single {
  position: relative;
  background-color: var(--grdeen-gray2, #f1f4f1);
  border-radius: 10px;
  padding: 38px 44px 45px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .service-sidebar__single {
    padding-left: 30px;
    padding-right: 30px;
  }
}
.service-sidebar__single + .service-sidebar__single {
  margin-top: 30px;
}
.service-sidebar__title {
  font-weight: 600;
  color: var(--grdeen-black, #172000);
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 20px;
  margin: 0 0 23px;
  position: relative;
}
.service-sidebar__title::before {
  position: relative;
  display: inline-block;
  font-family: "Font Awesome 5 Free";
  content: "\f787";
  font-size: 18px;
  color: var(--grdeen-base, #1a9120);
  margin-right: 4px;
}
.service-sidebar__nav {
  margin: 0;
  padding: 0;
}
.service-sidebar__nav li:not(:last-of-type) {
  margin-bottom: 8px;
}
.service-sidebar__nav li a {
  font-size: 15px;
  color: var(--grdeen-black, #172000);
  background-color: var(--grdeen-white, #fff);
  display: flex;
  font-weight: 600;
  justify-content: space-between;
  align-items: center;
  transition: all 500ms ease;
  padding: 11.3px 20px;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.service-sidebar__nav li a::after {
  font-family: "Font Awesome 5 Free";
  content: "\f787";
  font-weight: 900;
  font-size: inherit;
  color: currentColor;
}
.service-sidebar__nav li a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  background-color: var(--grdeen-base, #1a9120);
  z-index: -1;
  transform: scale(1, 0);
  perspective: 400px;
  visibility: hidden;
  transition: transform 500ms ease-in-out, visibility 500ms ease-in-out;
  transform-origin: bottom center;
}
.service-sidebar__nav li.current a, .service-sidebar__nav li:hover a {
  color: var(--grdeen-white, #fff);
}
.service-sidebar__nav li.current a::before, .service-sidebar__nav li:hover a::before {
  transform: scale(1, 1);
  visibility: visible;
  transform-origin: top center;
}
.service-sidebar__contact {
  position: relative;
  text-align: center;
  padding: 238px 20px 37px;
  background-size: cover;
  background-position: top center;
  background-repeat: no-repeat;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}
.service-sidebar__contact__title {
  margin: 0;
  color: var(--grdeen-white, #fff);
  font-size: 24px;
  margin-bottom: 21px;
  line-height: 32px;
  font-weight: 600;
}
.service-sidebar__contact .grdeen-btn {
  background-color: var(--grdeen-white, #fff);
  color: var(--grdeen-black, #172000);
  padding: 13.3px 43px;
  border: 1px solid var(--grdeen-base, #1a9120);
}
.service-sidebar__contact .grdeen-btn:hover {
  color: var(--grdeen-white, #fff);
  border-color: var(--grdeen-black, #172000);
}

/*--------------------------------------------------------------
# Blog details
--------------------------------------------------------------*/
.blog-details {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .blog-details {
    padding: 80px 0;
  }
}
.blog-details__image {
  position: relative;
  overflow: hidden;
  border-radius: 10px 10px 0 0;
}
.blog-details__image img {
  max-width: 100%;
  height: auto;
}
.blog-details__wrapper {
  position: relative;
  padding: 30px 28px 55px 38px;
  background-color: #f5fcf8;
}
@media (min-width: 1200px) {
  .blog-details__wrapper {
    margin-right: 10px;
  }
}
@media (max-width: 1199px) {
  .blog-details__wrapper {
    padding-left: 25px;
    padding-right: 20px;
  }
}
.blog-details__content {
  position: relative;
}
.blog-details__post-meta {
  display: flex;
  align-items: center;
  gap: 0 15px;
  margin: 0;
  margin-bottom: 10px;
}
.blog-details__post-meta li {
  color: var(--grdeen-text, #626f62);
  font-size: 13px;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.blog-details__post-meta li span {
  color: inherit;
  font-size: 14px;
  line-height: 1;
  margin-right: 6px;
}
.blog-details__post-meta li a {
  display: flex;
  align-items: center;
  color: inherit;
  transition: all 500ms ease;
}
.blog-details__post-meta li a:hover {
  color: var(--grdeen-base, #1a9120);
  text-shadow: 0 0 1px var(--grdeen-base, #1a9120);
}
.blog-details__title {
  font-size: 30px;
  line-height: 42px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin: 0 0 20px;
}
.blog-details__text {
  line-height: 32px;
  margin: 0 0 21px;
}
.blog-details__heading {
  font-size: 24px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin: 27px 0 16px;
}
.blog-details__list {
  position: relative;
  margin: 28px 0;
  padding: 0;
}
.blog-details__list li {
  font-size: 18px;
  line-height: 32px;
  font-weight: 500;
  color: var(--grdeen-base, #1a9120);
  padding-left: 34px;
  position: relative;
  margin: 0 0 5px;
}
.blog-details__list__icon {
  position: absolute;
  left: 0;
  top: 0;
  line-height: inherit;
  font-size: 20px;
}
.blog-details__meta {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 45px 0 0;
}
@media (max-width: 767px) {
  .blog-details__meta {
    display: block;
  }
}
.blog-details__cats {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-details__cats__title {
  text-transform: capitalize;
  margin: 0;
  line-height: 1em;
  font-size: 18px;
  font-weight: 600;
}
.blog-details__cats a {
  background-color: var(--grdeen-gray, #f6f7f2);
  text-transform: capitalize;
  font-size: 13px;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
  transition: all 500ms ease;
  display: inline-flex;
  padding: 4px 15px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 4px;
}
.blog-details__cats a:hover {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
  border-color: var(--grdeen-base, #1a9120);
}
.blog-details__tags {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 13px;
  font-weight: 500;
}
@media (max-width: 767px) {
  .blog-details__tags {
    margin-top: 20px;
  }
}
.blog-details__tags__title {
  text-transform: capitalize;
  margin: 0;
  line-height: 1em;
  font-size: 18px;
  font-weight: 600;
}
.blog-details__tags a {
  color: inherit;
  display: inline-block;
}
.blog-details__tags a:hover {
  color: var(--grdeen-base, #1a9120);
}
.blog-details__author {
  margin: 51px 0 0;
  position: relative;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 10px;
  display: flex;
  align-items: center;
  padding: 35px 28px 32px;
}
@media (max-width: 767px) {
  .blog-details__author {
    display: block;
  }
}
.blog-details__author__image {
  flex-shrink: 0;
  width: 124px;
  height: 162px;
  border-radius: 5px;
  overflow: hidden;
  margin-right: 25px;
}
@media (max-width: 767px) {
  .blog-details__author__image {
    margin: 0 0 25px;
  }
}
.blog-details__author__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.blog-details__author__name {
  font-size: 21px;
  line-height: 1;
  font-weight: 600;
  margin: 0 0 14px;
}
.blog-details__author__text {
  line-height: 32px;
  font-weight: 500;
  margin: 0 0 30px;
}
.blog-details__author__social {
  display: flex;
  align-items: center;
  gap: 10px;
}
.blog-details__author__social a {
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--grdeen-border-color, #e7e7e7);
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  transition: all 500ms ease;
  border-radius: 50%;
}
.blog-details__author__social a:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}

/*--------------------------------------------------------------
# Comments
--------------------------------------------------------------*/
.comments-one {
  margin-top: 45px;
}
@media (min-width: 1200px) {
  .comments-one {
    padding-left: 2px;
    padding-right: 12px;
  }
}
.comments-one__title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin-bottom: 14px;
}
.comments-one__list {
  margin: 0;
  margin-top: 40px;
}
.comments-one__card {
  margin-bottom: 45px;
  padding-bottom: 46px;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  display: flex;
  align-items: flex-start;
}
@media (max-width: 767px) {
  .comments-one__card {
    display: block;
  }
}
.comments-one__card__image {
  margin-right: 27px;
}
@media (max-width: 767px) {
  .comments-one__card__image {
    margin: 0 0 20px;
  }
}
.comments-one__card__image img {
  width: 103px;
  height: 103px;
  border-radius: 50%;
}
.comments-one__card__top {
  display: flex;
  align-items: center;
  gap: 10px;
  line-height: 1;
  margin-top: 5px;
  margin-bottom: 10px;
}
.comments-one__card__title {
  margin: 0;
  font-size: 18px;
  line-height: 1;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-weight: 700;
}
.comments-one__card__text {
  margin: 0;
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 9px;
}
.comments-one__card__date {
  margin: 0;
  font-size: 14px;
  color: var(--grdeen-base, #1a9120);
}
.comments-one__card__reply {
  padding: 5.5px 12px;
  font-size: 13px;
  font-weight: 500;
  text-transform: capitalize;
  background-color: var(--grdeen-border-color, #e7e7e7);
  color: var(--grdeen-black, #172000);
}
.comments-one__card__reply:hover {
  color: var(--grdeen-white, #fff);
}
.comments-one__card__reply::before {
  background-color: var(--grdeen-base, #1a9120);
}
.comments-one__card__content {
  position: relative;
}

.comments-form {
  margin-top: 38px;
}
.comments-form__title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin-bottom: 14px;
}
.comments-form__form {
  margin-top: 38px;
}
.comments-form__form .form-one__group {
  grid-template-columns: repeat(1, 1fr);
  gap: 20px;
}
.comments-form__form .bootstrap-select > .dropdown-toggle,
.comments-form__form input[type=text],
.comments-form__form input[type=phone],
.comments-form__form input[type=email],
.comments-form__form textarea {
  height: 60px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  transition: all 500ms ease;
}
.comments-form__form .bootstrap-select > .dropdown-toggle:focus,
.comments-form__form input[type=text]:focus,
.comments-form__form input[type=phone]:focus,
.comments-form__form input[type=email]:focus,
.comments-form__form textarea:focus {
  color: var(--grdeen-black, #172000);
}
.comments-form__form textarea {
  height: 195px;
  padding-top: 25px;
  margin-bottom: 6px;
}

/*--------------------------------------------------------------
# Shop
--------------------------------------------------------------*/
.product {
  position: relative;
}
.product__sidebar {
  position: relative;
}
.product__sidebar--title {
  text-transform: capitalize;
  display: inline-block;
  margin: 0;
  line-height: 1em;
  font-size: 20px;
  font-weight: 700;
  border-bottom: 1px solid currentColor;
  margin-top: 4px;
  margin-bottom: 30px;
}
.product__search {
  position: relative;
  display: block;
  margin-bottom: 30px;
}
.product__search form {
  border-radius: 0;
  position: relative;
}
.product__search form input[type=text] {
  width: 100%;
  height: 60px;
  background-color: transparent;
  padding-left: 30px;
  padding-right: 30px;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  outline: none;
  font-weight: 500;
  border-radius: 5px;
}
.product__price-ranger {
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 10px;
  padding: 33px 40px;
  margin-bottom: 30px;
}
.product__price-ranger #slider-range {
  margin: 5px 0 0 0px;
  background: var(--grdeen-border-color, #e7e7e7);
  border: none;
  height: 5px;
  border-radius: 5px;
  position: relative;
}
.product__price-ranger #slider-range .ui-slider-range {
  height: 100%;
  background: var(--grdeen-base, #1a9120);
}
.product__price-ranger #slider-range .ui-slider-handle {
  position: absolute;
  top: -5px;
  background: var(--grdeen-base, #1a9120);
  border: 0;
  height: 14px;
  width: 14px !important;
  border-radius: 50%;
  margin-left: -2px;
  outline: medium none;
  cursor: pointer;
  z-index: 2;
}
.product__price-ranger .ranger-min-max-block {
  position: relative;
  display: block;
  margin: 18px 0 0 0px;
}
.product__price-ranger .ranger-min-max-block input[type=text] {
  position: relative;
  display: inline-block;
  color: var(--grdeen-text, #626f62);
  font-size: 16px;
  font-weight: 500;
  width: 40px;
  line-height: 30px;
  border: none;
  outline: none;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  padding: 0;
  text-align: center;
  background-color: transparent;
}
.product__price-ranger .ranger-min-max-block span {
  position: relative;
  display: inline-block;
  color: var(--grdeen-text, #626f62);
  font-size: 16px;
  font-weight: 500;
  line-height: 40px;
  left: -2px;
}
.product__price-ranger .ranger-min-max-block input[type=submit] {
  position: relative;
  display: block;
  background: var(--grdeen-base, #1a9120);
  font-family: var(--grdeen-font, "Inter", sans-serif);
  float: right;
  text-align: center;
  border: none;
  color: var(--grdeen-white, #fff);
  font-size: 10px;
  font-weight: 600;
  line-height: 38px;
  margin: 0;
  cursor: pointer;
  padding: 0 20px;
  height: 38px;
  border-radius: 30px;
  text-transform: uppercase;
  transition: all 500ms ease;
}
.product__price-ranger .ranger-min-max-block input[type=submit]:hover {
  background-color: var(--grdeen-text-dark, #07370a);
  color: var(--grdeen-white, #fff);
}
.product__categories {
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 10px;
  padding: 33px 40px;
}
.product__categories ul {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-left: -15px;
  margin-bottom: -10px;
}
.product__categories ul li {
  position: relative;
  margin: 0 0 4px;
}
.product__categories ul li a {
  position: relative;
  display: flex;
  align-items: center;
  line-height: 24px;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--grdeen-text, #626f62);
  font-weight: 500;
  transition: all 0.3s ease;
  z-index: 1;
  padding: 9px 15px 11px;
}
.product__categories ul li a span {
  position: relative;
  top: 1px;
  display: inline-block;
  font-size: 12px;
  color: var(--grdeen-black, #172000);
  margin-right: 10px;
  transition: all 0.3s ease;
}
.product__categories ul li a::before {
  content: "";
  position: absolute;
  top: 0;
  left: 16px;
  bottom: 0;
  width: 100%;
  background-color: var(--grdeen-base, #1a9120);
  z-index: -1;
  transform: scale(1, 0);
  perspective: 400px;
  visibility: hidden;
  border-radius: 5px;
  transition: transform 500ms ease-in-out, visibility 500ms ease-in-out;
  transform-origin: bottom center;
}
.product__categories ul li:hover a, .product__categories ul li.active a {
  color: var(--grdeen-white, #fff);
  padding-left: 40px;
}
.product__categories ul li:hover a::before, .product__categories ul li.active a::before {
  transform: scale(1, 1);
  visibility: visible;
  transform-origin: top center;
}
.product__categories ul li:hover a span, .product__categories ul li.active a span {
  color: var(--grdeen-white, #fff);
}
.product__categories ul li.active a {
  font-weight: 600;
}
.product__info-top {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 5px;
  padding: 0 28px;
  margin-bottom: 30px;
}
@media (max-width: 767px) {
  .product__info-top {
    display: block;
    padding: 20px;
  }
}
.product__showing-text {
  margin: 0;
  font-size: 16px;
}
.product__showing-sort {
  margin: 0;
}
.product__showing-sort .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  position: relative;
  display: block;
  width: 160px !important;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.product__showing-sort .bootstrap-select > .dropdown-toggle::after {
  display: none;
}
.product__showing-sort .bootstrap-select .dropdown-menu {
  border: none;
}
.product__showing-sort .bootstrap-select > .dropdown-toggle {
  position: relative;
  height: 53px;
  outline: none !important;
  border-radius: 0;
  border: 0;
  background-color: transparent !important;
  margin: 0;
  padding: 0;
  padding-left: 30px;
  padding-right: 0;
  color: var(--grdeen-text, #626f62) !important;
  font-size: 16px;
  line-height: 53px;
  font-weight: 400;
  box-shadow: none !important;
  background-repeat: no-repeat;
  background-size: 14px 12px;
  background-position: right 25.75px center;
}
@media (max-width: 767px) {
  .product__showing-sort .bootstrap-select > .dropdown-toggle {
    padding-left: 0;
  }
}
.product__showing-sort .bootstrap-select > .dropdown-toggle:before {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -10px;
  font-family: "icomoon" !important;
  content: "\e924";
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
}
.product__showing-sort .bootstrap-select .dropdown-menu > li + li > a {
  border-top: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.product__showing-sort .bootstrap-select .dropdown-menu > li > a {
  font-size: 16px;
  font-weight: 400;
  padding: 8px 25px;
  color: var(--grdeen-text, #626f62);
  background-color: var(--grdeen-gray, #f6f7f2);
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}
.product__showing-sort .bootstrap-select .dropdown-menu > li:hover > a,
.product__showing-sort .bootstrap-select .dropdown-menu > li.selected > a {
  background: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  border-color: var(--grdeen-base, #1a9120);
}
.product__item {
  position: relative;
  transition: all 500ms ease;
}
.product__item__img {
  position: relative;
  overflow: hidden;
  background-color: #ecfaf2;
  border-radius: 30px 30px 0 0;
}
.product__item__img img {
  width: 100%;
  height: auto;
  transition: all 500ms ease;
  transform: scale(1);
}
.product__item__label {
  position: absolute;
  left: 20px;
  top: 20px;
  z-index: 1;
}
.product__item__label__sale {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  font-size: 14px;
  display: block;
  border-radius: 30px;
  padding: 6px 13px;
  line-height: 1;
}
.product__item__btn {
  position: relative;
  width: 100%;
  z-index: 2;
}
.product__item__btn .grdeen-btn {
  width: 100%;
  background-color: #ceebda;
  border-radius: 0;
  color: var(--grdeen-black, #172000);
}
.product__item__btn .grdeen-btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.product__item__btn .grdeen-btn:hover {
  color: var(--grdeen-white, #fff);
}
.product__item__btn .grdeen-btn:hover::before {
  height: 1000%;
}
.product__item:hover .product__item__img img {
  transform: scale(1.05);
}
.product__item__content-wrap {
  position: relative;
  background-color: var(--grdeen-white, #fff);
  box-shadow: 0px 0px 18px 0px rgba(229, 230, 232, 0.72);
  margin-top: -48px;
  border-radius: 0 0 30px 30px;
}
.product__item__content {
  position: relative;
  padding: 31px 22px 26px;
}
.product__item__ratings {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #ed8a19;
  letter-spacing: 2px;
  font-weight: normal;
  position: absolute;
  right: 18px;
  bottom: 33px;
}
@media (min-width: 992px) and (max-width: 1199px) {
  .product__item__ratings {
    position: relative;
    bottom: 0;
    right: 0;
    margin: 15px 0 0;
  }
}
.product__item__title {
  font-size: 18px;
  line-height: 26px;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin: 0;
  margin-bottom: 8px;
}
.product__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.product__item__title a:hover {
  background-size: 100% 1px;
}
.product__item__title a:hover {
  color: var(--grdeen-base, #1a9120);
}
.product__item__price {
  display: flex;
  align-items: center;
  font-size: 18px;
  color: var(--grdeen-text-dark, #07370a);
  line-height: 1em;
  font-weight: 500;
  gap: 10px;
  margin: 0;
}
.product__item__price__offer {
  color: #abb2b8;
  text-decoration: line-through;
}

.product-one {
  padding: 120px 0;
}
@media (max-width: 767px) {
  .product-one {
    padding: 80px 0;
  }
}
@media (min-width: 992px) {
  .product-one__carousel .owl-nav {
    display: none;
  }
}

.product-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 9px;
  margin-top: 30px;
}
@media (max-width: 767px) {
  .product-pagination {
    margin-top: 10px;
    gap: 5px;
  }
}
.product-pagination a,
.product-pagination span {
  width: 45px;
  height: 48px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  background-color: transparent;
  color: var(--grdeen-text-dark, #07370a);
  border-radius: 20px;
  transition: all 500ms ease;
}
.product-pagination a:hover, .product-pagination a.current,
.product-pagination span:hover,
.product-pagination span.current {
  border-color: var(--grdeen-base, #1a9120);
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.product-pagination .prev {
  width: auto;
  font-size: 13px;
  text-transform: uppercase;
  padding: 0 13px;
  margin-right: 10px;
}
.product-pagination .next {
  width: auto;
  font-size: 13px;
  text-transform: uppercase;
  padding: 0 13px;
  margin-left: 10px;
}

/*--------------------------------------------------------------
# Shop details
--------------------------------------------------------------*/
.product-details {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .product-details {
    padding: 80px 0;
  }
}
.product-details__slider {
  position: relative;
}
@media (min-width: 1200px) {
  .product-details__slider {
    margin-right: 35px;
  }
}
.product-details__slider__image {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.product-details__slider__image img {
  max-width: 100%;
  height: auto;
}
.product-details__carousel {
  position: relative;
  border-radius: 30px;
  background-color: #f4fcf7;
  text-align: center;
}
.product-details__carousel.owl-carousel .owl-nav .owl-prev {
  position: absolute;
  left: 11px;
  bottom: 88px;
  width: 57px;
  height: 57px;
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  margin: 0;
  padding: 0;
  transition: all 500ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--grdeen-text-dark, #07370a);
}
.product-details__carousel.owl-carousel .owl-nav .owl-prev:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.product-details__carousel.owl-carousel .owl-nav .owl-next {
  position: absolute;
  right: 11px;
  bottom: 88px;
  width: 57px;
  height: 57px;
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  margin: 0;
  padding: 0;
  transition: all 500ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: var(--grdeen-text-dark, #07370a);
}
.product-details__carousel.owl-carousel .owl-nav .owl-next:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.product-details__carousel-thumb {
  max-width: 266px;
  margin: -45px auto 0;
  position: relative;
}
.product-details__carousel-thumb .item {
  position: relative;
  padding: 0 0 0;
}
.product-details__carousel-thumb__item {
  width: 130px;
  height: 96px;
  background-color: var(--grdeen-white, #fff);
  border: 2px solid var(--grdeen-base, #1a9120);
  border-radius: 25px;
  object-fit: cover;
  display: block;
  text-align: center;
  position: relative;
  padding: 15px;
}
.product-details__carousel-thumb__item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.product-details__content {
  position: relative;
  margin-top: 4px;
}
@media (min-width: 1200px) {
  .product-details__content {
    margin-left: 20px;
  }
}
@media (max-width: 991px) {
  .product-details__content {
    margin: 45px 0 0;
  }
}
.product-details__title {
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 20px;
}
@media (max-width: 767px) {
  .product-details__title {
    font-size: 28px;
  }
}
.product-details__price {
  display: flex;
  align-items: center;
  position: relative;
}
.product-details__price__regular {
  display: block;
  font-size: 28px;
  color: var(--grdeen-black, #172000);
  font-weight: 500;
  margin: 0;
}
.product-details__price__offer {
  display: block;
  font-size: 14px;
  color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.5);
  text-decoration: line-through;
  margin-left: 22px;
}
.product-details__price__off {
  display: block;
  font-size: 14px;
  color: #f42647;
  padding-left: 5px;
}
.product-details__price__stock {
  font-size: 13px;
  line-height: 1;
  display: inline-block;
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-base, #1a9120);
  padding: 4px 5.5px;
  margin-left: 6px;
}
.product-details__review {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #efce4a;
  letter-spacing: 3px;
  margin-bottom: 34px;
}
.product-details__review a {
  display: inline-block;
  color: var(--grdeen-black, #172000);
  font-size: 14px;
  letter-spacing: 0;
  margin-left: 4px;
  font-weight: 400;
  transition: all 500ms ease;
}
.product-details__review a:hover {
  color: var(--grdeen-base, #1a9120);
}
.product-details__excerpt {
  font-size: 15px;
  line-height: 30px;
  color: var(--grdeen-black, #172000);
  font-weight: 400;
  margin: 0 0 22px;
}
.product-details__feature {
  padding: 0;
  margin: 0 0 22px;
  list-style: none;
}
.product-details__feature li {
  list-style: none;
  font-size: 16px;
  line-height: 32px;
  font-weight: 400;
  color: var(--grdeen-text, #626f62);
  position: relative;
  padding-left: 28px;
}
.product-details__feature__icon {
  position: absolute;
  left: 0;
  top: 1px;
  color: var(--grdeen-black, #172000);
  font-size: 16px;
}
.product-details__qty {
  position: relative;
  display: flex;
  align-items: end;
  gap: 27px;
  margin: 0 0 35px;
}
@media (max-width: 767px) {
  .product-details__qty {
    flex-wrap: wrap;
  }
}
.product-details__quantity {
  position: relative;
}
.product-details__quantity__title {
  margin: 0 0 12px;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 15px;
  font-weight: 500;
}
.product-details__quantity .quantity-box {
  position: relative;
  width: 115px;
  height: 44px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.product-details__quantity .quantity-box input {
  width: 60px;
  height: 42px;
  border: none;
  outline: none;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
  appearance: textfield;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  color: var(--grdeen-black, #172000);
  padding-left: 21px;
  outline: none;
  font-size: 16px;
  font-weight: 500;
  color: #131216;
  background-color: transparent;
}
.product-details__quantity .quantity-box button {
  width: 24px;
  height: 24px;
  color: var(--grdeen-black, #172000);
  font-size: 14px;
  position: absolute;
  top: 2px;
  right: 9px;
  background-color: transparent;
  border: none;
  border-left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
  transition: all 500ms ease;
  text-shadow: 0px 0px 1px var(--grdeen-text, #626f62);
  padding: 0;
}
.product-details__quantity .quantity-box button.sub {
  bottom: 2px;
  top: auto;
  border-top: 0;
}
.product-details__quantity .quantity-box button:hover {
  color: var(--grdeen-base, #1a9120);
}
.product-details__buttons {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 27px;
}
.product-details__buttons .grdeen-btn {
  font-size: 15px;
  text-transform: none;
  padding: 14px 37px;
}
@media (max-width: 767px) {
  .product-details__buttons {
    width: 100%;
  }
}
.product-details__buttons__wishlist {
  height: 44px;
  width: 44px;
  background-color: var(--grdeen-gray, #f6f7f2);
  color: var(--grdeen-black, #172000);
  border-radius: 50%;
  font-size: 19px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 500ms ease;
}
.product-details__buttons__wishlist:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.product-details__info {
  padding: 0;
  margin: 0 0 32px;
  list-style: none;
}
.product-details__info li {
  list-style: none;
  font-size: 15px;
  line-height: 32px;
  font-weight: 400;
  color: var(--grdeen-black, #172000);
  position: relative;
  padding-left: 28px;
}
.product-details__info li strong {
  font-weight: 600;
}
.product-details__info__icon {
  position: absolute;
  left: 0;
  top: 1px;
  color: var(--grdeen-black, #172000);
  font-size: 16px;
}
.product-details__socials {
  position: relative;
  display: flex;
  align-items: center;
}
.product-details__socials__title {
  margin: 0 14px 0 0;
  font-size: 16px;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  color: var(--grdeen-text, #626f62);
}
.product-details__socials a {
  display: inline-block;
  font-size: 16px;
  color: var(--grdeen-text-dark, #07370a);
}
.product-details__socials a:hover {
  color: var(--grdeen-base, #1a9120);
}
.product-details__socials a + a {
  margin-left: 22px;
}
.product-details__tabs {
  position: relative;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 10px;
  margin-top: 95px;
  padding: 0 0 52px;
}
.product-details__tabs__list {
  margin: 0 0 37px;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
@media (max-width: 767px) {
  .product-details__tabs__list {
    flex-wrap: wrap;
  }
}
.product-details__tabs__list .tab-btn {
  position: relative;
  background-color: var(--grdeen-gray, #f6f7f2);
  color: var(--grdeen-black, #172000);
  cursor: pointer;
  border-radius: 0 0 20px 20px;
  font-size: 18px;
  font-weight: 500;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  text-transform: capitalize;
  padding: 12.5px 56px;
  transition: all 500ms ease;
}
@media (max-width: 767px) {
  .product-details__tabs__list .tab-btn {
    padding: 12.5px 26px;
  }
}
.product-details__tabs__list .tab-btn:hover, .product-details__tabs__list .tab-btn.active-btn {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.product-details__tabs__description {
  position: relative;
  padding: 0 65px;
  font-size: 16px;
  line-height: 30px;
  color: var(--grdeen-text-dark, #07370a);
}
@media (max-width: 991px) {
  .product-details__tabs__description {
    padding: 0 35px;
  }
}
@media (max-width: 767px) {
  .product-details__tabs__description {
    padding: 0 25px;
  }
}
.product-details__tabs__description__text {
  margin: 0 0 29px;
}
.product-details__tabs__description__text:last-child {
  margin: 0;
}
.product-details__tabs__specfication {
  position: relative;
  padding: 0 70px;
}
@media (max-width: 991px) {
  .product-details__tabs__specfication {
    padding: 0 35px;
  }
}
@media (max-width: 767px) {
  .product-details__tabs__specfication {
    padding: 0 25px;
  }
}
.product-details__tabs__specfication table {
  position: relative;
  width: 100%;
  border: none;
  margin: 0;
}
.product-details__tabs__specfication table th {
  line-height: 36px;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 16px;
  font-weight: 600;
  width: 500px;
  margin: 0 0 0;
  padding: 9px 0;
  background: transparent !important;
  border: none;
  border-bottom: 1px dashed var(--grdeen-border-color, #e7e7e7);
}
@media (max-width: 1199px) {
  .product-details__tabs__specfication table th {
    width: 300px;
  }
}
@media (max-width: 991px) {
  .product-details__tabs__specfication table th {
    width: 200px;
  }
}
@media (max-width: 767px) {
  .product-details__tabs__specfication table th {
    width: auto;
    min-height: 120px;
  }
}
.product-details__tabs__specfication table td {
  color: var(--grdeen-text, #626f62);
  font-size: 16px;
  font-weight: 400;
  font-style: normal;
  margin: 0;
  background: transparent !important;
  border: none;
  border-bottom: 1px dashed var(--grdeen-border-color, #e7e7e7);
}
.product-details__tabs__comment {
  position: relative;
  padding: 0 65px;
}
@media (max-width: 991px) {
  .product-details__tabs__comment {
    padding: 0 35px;
  }
}
@media (max-width: 767px) {
  .product-details__tabs__comment {
    padding: 0 25px;
  }
}
.product-details__comment {
  position: relative;
}
.product-details__comment__title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin-bottom: 14px;
}
.product-details__comment__list {
  margin: 0;
  margin-top: 40px;
}
.product-details__comment__card {
  margin-bottom: 45px;
  padding-bottom: 46px;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  display: flex;
  align-items: flex-start;
}
@media (max-width: 767px) {
  .product-details__comment__card {
    display: block;
  }
}
.product-details__comment__card__image {
  margin-right: 27px;
}
@media (max-width: 767px) {
  .product-details__comment__card__image {
    margin: 0 0 20px;
  }
}
.product-details__comment__card__image img {
  width: 103px;
  height: 103px;
  border-radius: 50%;
}
.product-details__comment__card__top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  line-height: 1;
  margin-top: 5px;
  margin-bottom: 10px;
}
.product-details__comment__card__title {
  margin: 0;
  font-size: 18px;
  line-height: 1;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-weight: 700;
}
.product-details__comment__card__text {
  margin: 0;
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 9px;
}
.product-details__comment__card__date {
  margin: 0;
  font-size: 14px;
  color: var(--grdeen-base, #1a9120);
}
.product-details__comment__card__ratings {
  font-size: 14px;
  letter-spacing: 3px;
  color: #efce4a;
}
.product-details__comment__card__content {
  position: relative;
}
.product-details__form {
  position: relative;
  margin: 51px 0 0;
}
.product-details__form .row {
  --bs-gutter-x: 20px;
}
.product-details__form-title {
  margin: 0;
  font-size: 22px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin-bottom: 14px;
}
.product-details__form-ratings {
  display: flex;
  align-items: center;
  letter-spacing: 6px;
  font-size: 16px;
  color: var(--grdeen-base, #1a9120);
  margin: 0 0 15px;
}
.product-details__form-ratings__label {
  display: inline-block;
  font-size: 18px;
  letter-spacing: 0;
  color: var(--grdeen-text, #626f62);
  margin: 0 17px 0 0;
}
.product-details__form__form {
  margin-top: 0;
}
@media (min-width: 575px) {
  .product-details__form__form .form-one__group {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
}

.related-product {
  position: relative;
  padding: 0 0 120px;
}
@media (max-width: 767px) {
  .related-product {
    padding-bottom: 80px;
  }
}
.related-product__title {
  font-size: 42px;
  font-weight: 700;
  margin: 0 0 40px;
}
@media (max-width: 767px) {
  .related-product__title {
    font-size: 35px;
    margin-bottom: 30px;
  }
}
.related-product .grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots {
  display: none;
}

/*--------------------------------------------------------------
# Cart
--------------------------------------------------------------*/
.cart-page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .cart-page {
    padding: 80px 0;
  }
}
.cart-page .col-lg-8 {
  width: 69.3%;
}
@media (max-width: 991px) {
  .cart-page .col-lg-8 {
    width: 100%;
  }
}
.cart-page .col-lg-4 {
  width: 30.7%;
}
@media (max-width: 991px) {
  .cart-page .col-lg-4 {
    width: 100%;
  }
}
.cart-page .table-responsive {
  position: relative;
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
@media (max-width: 1199px) {
  .cart-page .table-responsive {
    margin-bottom: 50px;
  }
}
.cart-page__table {
  position: relative;
  width: 100%;
  border: none;
  margin: 0 0 31px;
}
@media (max-width: 1199px) {
  .cart-page__table {
    min-width: 802px;
  }
}
.cart-page__table thead tr th {
  color: var(--grdeen-black, #172000);
  font-size: 18px;
  line-height: 30px;
  font-weight: 600;
  padding: 0 0 4px;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  text-transform: capitalize;
  border: none;
  background-color: transparent;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7) !important;
  box-shadow: none;
}
.cart-page__table thead tr th:last-child {
  text-align: right;
}
.cart-page__table tbody tr td {
  font-size: 16px;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
  vertical-align: middle;
  border: none;
  box-shadow: none;
  background-color: transparent;
  border-top: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  padding: 30px 0;
  letter-spacing: 0;
}
.cart-page__table tbody tr td:last-child {
  text-align: right;
}
.cart-page__table__meta {
  display: flex;
  align-items: center;
}
.cart-page__table__meta__remove {
  position: absolute;
  right: -12px;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 24px;
  height: 24px;
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  font-size: 12px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-page__table__meta__remove:hover {
  background-color: var(--grdeen-black, #172000);
}
.cart-page__table__meta__img {
  width: 83px;
  height: 93px;
  border-radius: 4px;
  background-color: var(--grdeen-gray, #f6f7f2);
  margin-right: 26px;
  position: relative;
}
.cart-page__table__meta__img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
.cart-page__table__meta__title {
  font-size: 16px;
  line-height: 26px;
  margin: 0;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  color: var(--grdeen-black, #172000);
}
.cart-page__table__meta__title a {
  color: inherit;
}
.cart-page__table__meta__title a:hover {
  color: var(--grdeen-base, #1a9120);
}
.cart-page__table .product-details__quantity .quantity-box {
  height: 60px;
  background-color: var(--grdeen-gray, #f6f7f2);
  width: 160px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 15px;
  border-radius: 5px;
}
.cart-page__table .product-details__quantity .quantity-box input {
  margin: 0 auto;
  padding: 0;
  text-align: center;
}
.cart-page__table .product-details__quantity .quantity-box button {
  position: relative;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  width: 35px;
  height: 35px;
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  color: var(--grdeen-black, #172000);
}
.cart-page__table .product-details__quantity .quantity-box button:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.cart-page__coupone-form-title {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin: 0 0 20px;
}
.cart-page__coupone-form {
  position: relative;
  margin: 0 0 26px;
}
.cart-page__coupone-form input[type=text] {
  height: 58px;
  width: 100%;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  background-color: transparent;
  padding-left: 30px;
  padding-right: 30px;
  outline: none;
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-weight: 400;
  border-radius: 5px;
}
.cart-page__coupone-form .grdeen-btn {
  position: absolute;
  right: 0;
  top: 1px;
  padding: 18.25px 48px;
}
@media (max-width: 767px) {
  .cart-page__coupone-form .grdeen-btn {
    position: relative;
    margin: 10px 0 0;
  }
}
.cart-page__link {
  color: var(--grdeen-base, #1a9120);
  font-weight: 600;
  font-size: 15px;
  line-height: 1.1;
  display: inline-block;
  text-transform: uppercase;
  position: relative;
  transition: all 0.5s ease;
}
.cart-page__link::after {
  content: "";
  position: absolute;
  width: 100%;
  max-width: 100%;
  height: 1.1px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  margin: 0 auto;
  background-color: var(--grdeen-base, #1a9120);
  transition: all 0.5s ease;
}
.cart-page__link:hover {
  color: var(--grdeen-black, #172000);
}
.cart-page__link:hover::after {
  max-width: 70%;
  background-color: var(--grdeen-black, #172000);
}
.cart-page__cart-total {
  position: relative;
  background-color: var(--grdeen-gray, #f6f7f2);
  margin: 0 0 0;
  padding: 0;
}
@media (min-width: 1200px) {
  .cart-page__cart-total {
    margin-left: 10px;
  }
}
.cart-page__cart-total li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  color: var(--grdeen-black, #172000);
  font-weight: 600;
  padding: 15px 25px;
}
.cart-page__cart-total li:first-child {
  background-color: var(--grdeen-border-color, #e7e7e7);
}
.cart-page__cart-total li + li {
  border-top: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.cart-page__cart-total__amount {
  position: relative;
}
.cart-page__cart-total__shipping {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: var(--grdeen-text, #626f62);
}
.cart-page__cart-total__shipping__rate {
  font-size: 16px;
  color: var(--grdeen-black, #172000);
}
.cart-page__cart-total__shipping__link {
  color: var(--grdeen-base, #1a9120);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.1;
  display: inline-block;
  position: relative;
  margin-top: 10px;
  transition: all 0.5s ease;
}
.cart-page__cart-total__shipping__link::after {
  content: "";
  position: absolute;
  width: 100%;
  max-width: 85%;
  height: 1.1px;
  right: 0;
  bottom: 0;
  z-index: 1;
  margin: 0 auto;
  background-color: var(--grdeen-base, #1a9120);
  transition: all 0.5s ease;
}
.cart-page__cart-total__shipping__link:hover {
  color: var(--grdeen-black, #172000);
}
.cart-page__cart-total__shipping__link:hover::after {
  background-color: var(--grdeen-black, #172000);
}
.cart-page__buttons {
  position: relative;
}
.cart-page__buttons .grdeen-btn {
  width: 100%;
  background-color: var(--grdeen-text-dark, #07370a);
}
.cart-page__buttons .grdeen-btn::before {
  left: -45px;
  background-color: var(--grdeen-base, #1a9120);
}
.cart-page__buttons .grdeen-btn:hover:before {
  height: 550%;
}

/*--------------------------------------------------------------
# Checkout
--------------------------------------------------------------*/
.checkout-page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .checkout-page {
    padding: 80px 0;
  }
}
.checkout-page .bs-gutter-x-20 {
  --bs-gutter-x: 30px;
}
.checkout-page__billing-address {
  position: relative;
}
.checkout-page__billing-address__title {
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 20px;
  margin: 0 0 29px;
  font-weight: 600;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
  padding-bottom: 13px;
}
.checkout-page__label-text {
  line-height: 1;
  color: var(--grdeen-black, #172000);
  font-weight: 500;
  margin-bottom: 19px;
  display: block;
}
.checkout-page__input-box {
  position: relative;
  line-height: 1;
  margin: 0 0 30px;
}
.checkout-page__input-box input[type=text],
.checkout-page__input-box input[type=email],
.checkout-page__input-box input[type=tel] {
  height: 60px;
  width: 100%;
  border: none;
  background-color: transparent;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  padding-left: 20px;
  padding-right: 20px;
  outline: none;
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  font-family: var(--grdeen-font, "Inter", sans-serif);
  display: block;
  font-weight: 400;
  border-radius: 4px;
}
.checkout-page__input-box .bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
  position: relative;
  display: block;
  width: 100% !important;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.checkout-page__input-box .bootstrap-select > .dropdown-toggle::after {
  display: none;
}
.checkout-page__input-box .bootstrap-select > .dropdown-toggle {
  position: relative;
  height: 60px;
  outline: none !important;
  border-radius: 4px;
  border: 0;
  background-color: transparent !important;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  margin: 0;
  padding: 0;
  padding-left: 20px;
  padding-right: 20px;
  color: var(--grdeen-text, #626f62) !important;
  font-size: 15px;
  line-height: 56px;
  font-weight: 400;
  box-shadow: none !important;
  background-repeat: no-repeat;
  background-size: 14px 12px;
  background-position: right 25.75px center;
}
.checkout-page__input-box .bootstrap-select > .dropdown-toggle:before {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 30px;
  font-family: "Font Awesome 5 Free";
  content: "\f107";
  font-weight: 900;
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
}
.checkout-page__input-box .bootstrap-select .dropdown-menu > li + li > a {
  border-top: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.checkout-page__input-box .bootstrap-select .dropdown-menu {
  border: none;
}
.checkout-page__input-box .bootstrap-select .dropdown-menu > li > a {
  font-size: 15px;
  font-weight: 400;
  padding: 15px 20px;
  color: var(--grdeen-text, #626f62);
  background-color: var(--grdeen-gray, #f6f7f2);
  transition: all 0.4s ease;
}
.checkout-page__input-box .bootstrap-select .dropdown-menu > li:hover > a,
.checkout-page__input-box .bootstrap-select .dropdown-menu > li.selected > a {
  background: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  border-color: var(--grdeen-base, #1a9120);
}
.checkout-page__input-box textarea {
  font-size: 15px;
  font-weight: 400;
  color: var(--grdeen-text, #626f62);
  height: 120px;
  width: 100%;
  background-color: transparent;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  padding: 20px 20px 30px 20px;
  border: none;
  outline: none;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  resize: none;
  border-radius: 18px;
  margin-bottom: 0px;
}
.checkout-page__check-box {
  position: relative;
  display: block;
  margin-top: -5px;
  margin-bottom: 10px;
}
.checkout-page__check-box input[type=checkbox] {
  display: none;
}
.checkout-page__check-box label {
  position: relative;
  display: block;
  padding-left: 30px;
  margin-right: 0px;
  margin-bottom: 0;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
  font-size: 15px;
  line-height: 24px;
  text-transform: none;
  cursor: pointer;
}
.checkout-page__check-box label span:before {
  position: absolute;
  top: 2px;
  left: 3px;
  display: block;
  border-bottom: 2px solid var(--grdeen-border-color, #e7e7e7);
  border-right: 2px solid var(--grdeen-border-color, #e7e7e7);
  content: "";
  width: 5px;
  height: 8px;
  pointer-events: none;
  transform-origin: 66% 66%;
  transform: rotate(45deg);
  transition: all 0.15s ease-in-out;
  opacity: 0;
}
.checkout-page__check-box input[type=checkbox] + label span {
  position: absolute;
  top: 4px;
  left: 0;
  width: 16px;
  height: 16px;
  background-color: var(--grdeen-white, #fff);
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  vertical-align: middle;
  cursor: pointer;
  transition: all 300ms ease;
}
.checkout-page__check-box input[type=checkbox]:checked + label span:before {
  opacity: 1;
}
.checkout-page__place-order {
  position: relative;
  margin-top: 25px;
}
.checkout-page__place-order .checkout-page__input-box {
  margin: 0;
}
.checkout-page__place-order .checkout-page__label-text {
  font-size: 18px;
  font-weight: 600;
}
.checkout-page__order-total {
  position: relative;
  background-color: var(--grdeen-gray, #f6f7f2);
  border: 1px solid var(--grdeen-black, #172000);
  border-radius: 5px;
  margin: 0 0;
  padding: 10px 20px;
}
@media (max-width: 991px) {
  .checkout-page__order-total {
    margin-top: 50px;
  }
}
.checkout-page__order-total__title {
  font-size: 20px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  padding: 15px 0;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.checkout-page__order {
  position: relative;
}
@media (min-width: 1200px) {
  .checkout-page__order {
    padding-left: 40px;
  }
}
.checkout-page__order__product {
  display: flex;
  align-items: center;
  gap: 13px;
}
.checkout-page__order__product__img {
  width: 52px;
  height: 55px;
  border-radius: 4px;
  background-color: var(--grdeen-gray, #f6f7f2);
  position: relative;
}
.checkout-page__order__product__img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
.checkout-page__order__product__title {
  font-size: 14px;
  line-height: 22px;
  margin: 0;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  color: var(--grdeen-black, #172000);
}
.checkout-page__order__product__title a {
  color: inherit;
}
.checkout-page__order__product__title a:hover {
  color: var(--grdeen-base, #1a9120);
}
.checkout-page__order__list {
  position: relative;
  padding: 0;
  margin: 0;
}
.checkout-page__order li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 18px;
  color: var(--grdeen-black, #172000);
  font-weight: 600;
  padding: 15px 0;
}
.checkout-page__order li + li {
  border-top: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.checkout-page__order__amount {
  position: relative;
}
.checkout-page__order__shipping {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: var(--grdeen-text, #626f62);
}
.checkout-page__order__shipping__rate {
  font-size: 16px;
  color: var(--grdeen-black, #172000);
}
.checkout-page__order__shipping__link {
  color: var(--grdeen-base, #1a9120);
  font-weight: 400;
  font-size: 14px;
  line-height: 1.1;
  display: inline-block;
  position: relative;
  margin-top: 10px;
  transition: all 0.5s ease;
}
.checkout-page__order__shipping__link::after {
  content: "";
  position: absolute;
  width: 100%;
  max-width: 85%;
  height: 1.1px;
  right: 0;
  bottom: 0;
  z-index: 1;
  margin: 0 auto;
  background-color: var(--grdeen-base, #1a9120);
  transition: all 0.5s ease;
}
.checkout-page__order__shipping__link:hover {
  color: var(--grdeen-black, #172000);
}
.checkout-page__order__shipping__link:hover::after {
  background-color: var(--grdeen-black, #172000);
}
.checkout-page__payment {
  background-color: var(--grdeen-gray, #f6f7f2);
  border: 1px solid var(--grdeen-black, #172000);
  border-radius: 5px;
  padding: 10px 20px 40px;
  margin-top: 30px;
}
.checkout-page__payment__heading {
  font-size: 20px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  padding: 15px 0 10px;
  margin: 0 0 35px;
  border-bottom: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.checkout-page__payment__item {
  position: relative;
  border: 1px solid var(--grdeen-black, #172000);
  border-radius: 5px;
  padding: 12px 20px;
  margin: 0 0 10px;
  transition: all 500ms ease;
}
.checkout-page__payment__item--active {
  border-color: var(--grdeen-base, #1a9120);
}
.checkout-page__payment__title {
  display: flex;
  font-size: 16px;
  margin: 0;
  align-items: center;
  cursor: pointer;
  font-weight: 600;
  color: var(--grdeen-black, #172000);
}
.checkout-page__payment__title::before {
  content: "";
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: 1px solid var(--grdeen-black, #172000);
  border-radius: 50%;
  margin-right: 10px;
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  font-size: 8px;
  line-height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  top: 0;
  transition: all 500ms ease;
}
.checkout-page__payment__item--active .checkout-page__payment__title::before {
  border-color: var(--grdeen-base, #1a9120);
  content: "\f00c";
  color: var(--grdeen-base, #1a9120);
}
.checkout-page__payment__content {
  font-size: 15px;
  line-height: 25px;
  padding: 15px 0 10px;
}
.checkout-page__payment__btn {
  position: relative;
  margin-top: 10px;
}
.checkout-page__payment__btn .grdeen-btn {
  width: 100%;
  background-color: var(--grdeen-text-dark, #07370a);
}
.checkout-page__payment__btn .grdeen-btn::before {
  left: -45px;
  background-color: var(--grdeen-base, #1a9120);
}
.checkout-page__payment__btn .grdeen-btn:hover::before {
  height: 550%;
}
.checkout-page__payment__condition {
  position: relative;
  display: block;
  margin: 20px 0;
}
.checkout-page__payment__condition input[type=checkbox] {
  display: none;
}
.checkout-page__payment__condition label {
  position: relative;
  display: block;
  padding-left: 30px;
  margin-right: 0px;
  margin-bottom: 0;
  font-weight: 500;
  color: var(--grdeen-black, #172000);
  font-size: 15px;
  line-height: 28px;
  text-transform: none;
  cursor: pointer;
}
.checkout-page__payment__condition label span:before {
  position: absolute;
  top: 2px;
  left: 3px;
  display: block;
  border-bottom: 2px solid var(--grdeen-border-color, #e7e7e7);
  border-right: 2px solid var(--grdeen-border-color, #e7e7e7);
  content: "";
  width: 5px;
  height: 8px;
  pointer-events: none;
  transform-origin: 66% 66%;
  transform: rotate(45deg);
  transition: all 0.15s ease-in-out;
  opacity: 0;
}
.checkout-page__payment__condition input[type=checkbox] + label span {
  position: absolute;
  top: 7px;
  left: 0;
  width: 16px;
  height: 16px;
  background-color: var(--grdeen-white, #fff);
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  vertical-align: middle;
  cursor: pointer;
  transition: all 300ms ease;
}
.checkout-page__payment__condition input[type=checkbox]:checked + label span:before {
  opacity: 1;
}

/*--------------------------------------------------------------
# Login
--------------------------------------------------------------*/
.login-page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .login-page {
    padding: 80px 0;
  }
}
.login-page__info {
  background-color: var(--grdeen-gray, #f6f7f2);
  padding: 13px 30px 15px;
  margin-bottom: 72px;
}
@media (max-width: 991px) {
  .login-page__info {
    margin-bottom: 20px;
  }
}
.login-page__info p {
  margin: 0;
  font-size: 14px;
}
.login-page__info p span {
  color: var(--grdeen-black, #172000);
}
.login-page__info p a {
  display: inline-block;
  color: var(--grdeen-base, #1a9120);
  transition: all 500ms ease;
}
.login-page__info p a:hover {
  color: var(--grdeen-black, #172000);
  text-shadow: 0 0 1px currentColor;
}
.login-page__wrap {
  position: relative;
}
.login-page__wrap__title {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 34px;
}
@media (max-width: 991px) {
  .login-page__wrap__title {
    margin-top: 50px;
  }
}
.login-page__form {
  position: relative;
  display: block;
  padding: 60px;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
}
@media (min-width: 991px) and (max-width: 1199px) {
  .login-page__form {
    padding: 40px;
  }
}
@media (max-width: 767px) {
  .login-page__form {
    padding: 30px 22px;
  }
}
.login-page__form-input-box {
  position: relative;
  display: block;
  margin-bottom: 20px;
}
.login-page__form-input-box input[type=email],
.login-page__form-input-box input[type=password] {
  height: 60px;
  width: 100%;
  border: none;
  background-color: var(--grdeen-gray, #f6f7f2);
  padding-left: 30px;
  padding-right: 30px;
  outline: none;
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  display: block;
  font-weight: 400;
}
.login-page__checked-box {
  position: relative;
  display: block;
  margin-top: -8px;
  margin-bottom: 15px;
}
.login-page__checked-box label {
  position: relative;
  display: inline-block;
  padding-left: 30px;
  margin-right: 0px;
  margin-bottom: 0;
  color: var(--grdeen-text, #626f62);
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
  text-transform: none;
  cursor: pointer;
}
.login-page__checked-box label span:before {
  position: absolute;
  top: 2px;
  left: 3px;
  display: block;
  border-bottom: 2px solid var(--grdeen-border-color, #e7e7e7);
  border-right: 2px solid var(--grdeen-border-color, #e7e7e7);
  content: "";
  width: 5px;
  height: 8px;
  pointer-events: none;
  -webkit-transform-origin: 66% 66%;
  -ms-transform-origin: 66% 66%;
  transform-origin: 66% 66%;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  opacity: 0;
}
.login-page__checked-box input[type=checkbox] {
  display: none;
}
.login-page__checked-box input[type=checkbox] + label span {
  position: absolute;
  top: 4px;
  left: 0;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  background: transparent;
  cursor: pointer;
  -webkit-transition: all 300ms ease;
  -moz-transition: all 300ms ease;
  -ms-transition: all 300ms ease;
  -o-transition: all 300ms ease;
  transition: all 300ms ease;
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
}
.login-page__checked-box input[type=checkbox]:checked + label span:before {
  opacity: 1;
}
.login-page__form-btn-box {
  position: relative;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.login-page__form-btn-box .grdeen-btn {
  padding-left: 47px;
  padding-right: 47px;
}
.login-page__form-forgot-password {
  position: relative;
  display: block;
  margin-left: 0px;
  flex: 0 0 100%;
  margin-top: 10px;
}
@media (min-width: 768px) {
  .login-page__form-forgot-password {
    margin-left: 20px;
    flex: 0 0 auto;
    margin-top: 0;
  }
}
.login-page__form-forgot-password a {
  font-size: 14px;
  font-weight: 500;
  color: var(--grdeen-text, #626f62);
  position: relative;
  display: inline-block;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.login-page__form-forgot-password a:hover {
  background-size: 100% 1px;
}
.login-page__form-forgot-password a:hover {
  color: var(--grdeen-base, #1a9120);
}

/*--------------------------------------------------------------
# error 404
--------------------------------------------------------------*/
.error-404 {
  position: relative;
  padding: 170px 0;
}
@media (max-width: 767px) {
  .error-404 {
    padding: 100px 0 80px;
  }
}
.error-404 .container {
  position: relative;
}
.error-404__shape {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 741px;
  height: 741px;
  background-color: var(--grdeen-gray, #f6f7f2);
  border-radius: 50%;
  animation: circleTranslate 3s linear infinite alternate;
}
@media (max-width: 991px) {
  .error-404__shape {
    width: 700px;
    height: 700px;
  }
}
@media (max-width: 767px) {
  .error-404__shape {
    display: none;
  }
}
@keyframes circleTranslate {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(10px, 10px);
  }
}
.error-404__content {
  position: relative;
  z-index: 1;
  max-width: 628px;
  margin: 0 auto;
  text-align: center;
}
.error-404__title {
  font-size: 180px;
  line-height: 1;
  font-weight: 900;
  margin-bottom: 20px;
  position: relative;
  color: #12110e;
  -webkit-text-stroke: 4px #49704b;
  display: inline-block;
}
@media (max-width: 767px) {
  .error-404__title {
    font-size: 140px;
  }
}
.error-404__title span {
  color: var(--grdeen-base, #1a9120);
  display: inline-block;
}
.error-404__title img {
  position: absolute;
  top: -50px;
  left: 0;
  right: 0;
  margin: 0 auto;
  animation: flowerRotate 5s linear 0s infinite;
}
@media (max-width: 767px) {
  .error-404__title img {
    max-width: 60px;
    top: -40px;
  }
}
.error-404__sub-title {
  font-size: 35px;
  color: var(--grdeen-base, #1a9120);
  margin: 0;
  margin-bottom: 43px;
  font-weight: 500;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
@media (max-width: 767px) {
  .error-404__sub-title {
    margin-bottom: 30px;
  }
}
.error-404__text {
  font-size: 20px;
  line-height: 32px;
  margin: 0;
  margin-bottom: 50px;
}
@media (max-width: 767px) {
  .error-404__text {
    margin-bottom: 30px;
  }
}
.error-404__search {
  position: relative;
  margin-bottom: 55px;
  width: 100%;
  display: block;
}
@media (max-width: 767px) {
  .error-404__search {
    margin-bottom: 30px;
  }
}
.error-404__search input[type=text] {
  border: none;
  outline: none;
  display: block;
  background-color: var(--grdeen-white, #fff);
  border: 1px solid var(--grdeen-border-color, #e7e7e7);
  border-radius: 5px;
  color: rgba(var(--grdeen-text-dark, #07370a), 0.75);
  font-size: 16px;
  width: 100%;
  padding-left: 27px;
  height: 60px;
  transition: all 400ms ease;
}
.error-404__search input[type=text]:focus {
  color: rgba(var(--grdeen-text-dark, #07370a), 1);
}
.error-404__search__btn {
  border: none;
  outline: none;
  box-shadow: none;
  position: absolute;
  top: 0;
  right: 0;
  font-size: 21px;
  border-radius: 5px;
  width: 60px;
  height: 60px;
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  transition: all 400ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.error-404__search__btn:hover {
  background-color: var(--grdeen-text-dark, #07370a);
  color: var(--grdeen-white, #fff);
}
.error-404__btn {
  background-color: var(--grdeen-text-dark, #07370a);
  padding: 20.3px 60px;
}
.error-404__btn::before {
  background-color: var(--grdeen-base, #1a9120);
}

/*--------------------------------------------------------------
# Faq
--------------------------------------------------------------*/
.faq-page {
  position: relative;
  padding: 112px 0 120px;
  background-color: #f3faf6;
}
@media (max-width: 767px) {
  .faq-page {
    padding: 75px 0 80px;
  }
}
.faq-page__sec-title {
  margin: 0;
  text-align: center;
  font-size: 43px;
  line-height: 50px;
  font-weight: bold;
  margin-bottom: 24px;
}
@media (max-width: 767px) {
  .faq-page__sec-title {
    font-size: 35px;
    line-height: 43px;
  }
}
.faq-page__sec-text {
  text-align: center;
  font-size: 18px;
  line-height: 30px;
  padding-bottom: 37px;
  margin-bottom: 46px;
  border-bottom: 1px solid #bccbbd;
}
@media (max-width: 991px) {
  .faq-page__sec-text br {
    display: none;
  }
}
.faq-page__title {
  margin: 0;
  font-size: 28px;
  line-height: 50px;
  font-weight: bold;
  margin-bottom: 27px;
}
.faq-page__accordion {
  position: relative;
}
.faq-page__accordion .accrodion {
  position: relative;
  margin-top: 12px;
  background-color: var(--grdeen-white, #fff);
  border-radius: 5px;
  overflow: hidden;
  transition: all 500ms ease;
  border-top: 1px solid var(--grdeen-white, #fff);
}
.faq-page__accordion .accrodion.active {
  border-color: var(--grdeen-base, #1a9120);
}
.faq-page__accordion .accrodion.active::after, .faq-page__accordion .accrodion.active::before {
  opacity: 1;
}
.faq-page__accordion .accrodion::before {
  position: absolute;
  right: 0;
  top: 0;
  width: 1px;
  content: "";
  background-color: var(--grdeen-base, #1a9120);
  height: 145px;
  opacity: 0;
  transition: all 500ms ease;
}
.faq-page__accordion .accrodion::after {
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  content: "";
  background-color: var(--grdeen-base, #1a9120);
  height: 145px;
  opacity: 0;
  transition: all 500ms ease;
}
.faq-page__accordion .accrodion-title {
  padding: 18px 60px 18px 29px;
  position: relative;
  cursor: pointer;
}
.faq-page__accordion .accrodion-title h4 {
  font-weight: 700;
  font-size: 20px;
  margin: 0;
  transition: all 500ms ease;
  position: relative;
}
.faq-page__accordion .accrodion-title__icon {
  width: 33px;
  height: 33px;
  position: absolute;
  top: 50%;
  right: -35px;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: #d0d8d3;
  transition: all 500ms ease;
}
.faq-page__accordion .accrodion-title__icon::after, .faq-page__accordion .accrodion-title__icon::before {
  width: 1px;
  height: 11px;
  position: absolute;
  background-color: #000;
  top: 50%;
  left: 50%;
  content: "";
  transform: translate(-50%, -50%);
  transition: all 500ms ease;
}
.faq-page__accordion .accrodion-title__icon::after {
  width: 11px;
  height: 1px;
}
.faq-page__accordion .active .accrodion-title__icon {
  background-color: var(--grdeen-base, #1a9120);
}
.faq-page__accordion .active .accrodion-title__icon::after, .faq-page__accordion .active .accrodion-title__icon::before {
  background-color: var(--grdeen-white, #fff);
  opacity: 0;
}
.faq-page__accordion .active .accrodion-title__icon::after {
  opacity: 1;
}
.faq-page__accordion .accrodion-content .inner {
  padding: 20px 29px 65px;
}
@media (min-width: 1200px) {
  .faq-page__accordion .accrodion-content .inner {
    padding-right: 55px;
  }
}
.faq-page__accordion .accrodion-content p {
  margin: 0;
  font-size: 16px;
  color: var(--grdeen-black, #172000);
  line-height: 32px;
}

/*--------------------------------------------------------------
# Package
--------------------------------------------------------------*/
.package-page {
  padding: 120px 0;
}
@media (max-width: 767px) {
  .package-page {
    padding: 80px 0;
  }
}

.package-card {
  transition: all 500ms ease;
  text-align: center;
  background-color: var(--grdeen-text-dark2, #0e150e);
  padding: 34px 25px 44px;
}
.package-card:hover .package-card__icon::after {
  border-style: dashed;
}
.package-card__icon-shape {
  position: absolute;
  right: 50px;
  top: 29px;
  width: 47px;
}
.package-card__icon-shape img {
  width: 100%;
  height: auto;
  animation: testimonialEffect2 1.3s linear infinite alternate;
}
.package-card__icon {
  width: 79px;
  height: 79px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  font-size: 45px;
  margin: 0 auto 20px;
  position: relative;
  border-radius: 50%;
  transition: all 500ms ease;
  margin: 0 auto 23px;
}
.package-card__icon::after {
  position: absolute;
  left: -8.5px;
  top: -8.5px;
  content: "";
  border: 1px solid var(--grdeen-white, #fff);
  border-radius: 50%;
  width: calc(100% + 17px);
  height: calc(100% + 17px);
  animation-duration: 1500ms;
  animation: rotated 10s infinite linear;
  transition: 500ms all ease;
  animation-play-state: running;
}
@keyframes rotated {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.package-card__price {
  color: var(--grdeen-base, #1a9120);
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 6px;
}
.package-card__title {
  margin: 0;
  font-weight: 600;
  color: var(--grdeen-white, #fff);
  font-size: 22px;
  margin-bottom: 20px;
}
.package-card__divider {
  height: 1px;
  width: 79%;
  margin: 0 auto;
  background-color: var(--grdeen-base, #1a9120);
}
.package-card__list {
  margin: 25px 0 34px;
  padding: 0;
  list-style: none;
}
.package-card__list li {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 7px;
  color: #a8b3a8;
  font-size: 16px;
  line-height: 32px;
}
.package-card__list__icon {
  color: var(--grdeen-base, #1a9120);
}
.package-card .grdeen-btn {
  font-size: 14px;
  border-radius: 3px;
  padding: 11px 33px;
}

/*--------------------------------------------------------------
# Animations
--------------------------------------------------------------*/
@keyframes bubbleMover {
  0% {
    -webkit-transform: translateY(0px) translateX(0) rotate(0);
    transform: translateY(0px) translateX(0) rotate(0);
  }
  30% {
    -webkit-transform: translateY(30px) translateX(50px) rotate(15deg);
    transform: translateY(30px) translateX(50px) rotate(15deg);
    -webkit-transform-origin: center center;
    transform-origin: center center;
  }
  50% {
    -webkit-transform: translateY(50px) translateX(100px) rotate(45deg);
    transform: translateY(50px) translateX(100px) rotate(45deg);
    -webkit-transform-origin: right bottom;
    transform-origin: right bottom;
  }
  80% {
    -webkit-transform: translateY(30px) translateX(50px) rotate(15deg);
    transform: translateY(30px) translateX(50px) rotate(15deg);
    -webkit-transform-origin: left top;
    transform-origin: left top;
  }
  100% {
    -webkit-transform: translateY(0px) translateX(0) rotate(0);
    transform: translateY(0px) translateX(0) rotate(0);
    -webkit-transform-origin: center center;
    transform-origin: center center;
  }
}
@keyframes shapeMover {
  0%, 100% {
    transform: perspective(400px) translateY(0) rotate(0deg) translateZ(0px) translateX(0);
  }
  50% {
    transform: perspective(400px) rotate(-45deg) translateZ(20px) translateY(20px) translateX(20px);
  }
}
@keyframes banner3Shake {
  0% {
    -webkit-transform: rotate3d(0, 1, 0, 0deg);
    transform: rotate3d(0, 1, 0, 0deg);
  }
  30% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  60% {
    -webkit-transform: rotate3d(1, 0, 0, 0deg);
    transform: rotate3d(1, 0, 0, 0deg);
  }
  80% {
    -webkit-transform: rotate3d(0, 0, 1, 5deg);
    transform: rotate3d(0, 0, 1, 5deg);
  }
  100% {
    -webkit-transform: rotate3d(0, 1, 0, 0deg);
    transform: rotate3d(0, 1, 0, 0deg);
  }
}
@keyframes squareMover {
  0%, 100% {
    -webkit-transform: translate(0, 0) rotate(0);
    transform: translate(0, 0) rotate(0);
  }
  20%, 60% {
    -webkit-transform: translate(20px, 40px) rotate(180deg);
    transform: translate(20px, 40px) rotate(180deg);
  }
  30%, 80% {
    -webkit-transform: translate(40px, 60px) rotate(0deg);
    transform: translate(40px, 60px) rotate(0deg);
  }
}
@keyframes treeMove {
  0%, 100% {
    -webkit-transform: rotate(0deg) translateX(0);
    transform: rotate(0deg) translateX(0);
  }
  25%, 75% {
    -webkit-transform: rotate(5deg) translateX(15px);
    transform: rotate(5deg) translateX(15px);
  }
  50% {
    -webkit-transform: rotate(10deg) translateX(30px);
    transform: rotate(10deg) translateX(30px);
  }
}
@keyframes leafMove {
  0%, 100% {
    -webkit-transform: rotate(0deg) translateX(0);
    transform: rotate(0deg) translateX(0);
  }
  25%, 75% {
    transform: rotate(-2deg) translateX(5px);
  }
  50% {
    transform: rotate(-4deg) translateX(10px);
  }
}
@keyframes messageMove {
  0%, 100% {
    transform: translateX(0);
  }
  25%, 75% {
    transform: translateX(5px);
  }
  50% {
    transform: translateX(10px);
  }
}
@keyframes textRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@keyframes flowerRotate {
  0%, 100% {
    transform: rotate(0deg);
  }
  25%, 75% {
    transform: rotate(5deg);
  }
  50% {
    transform: rotate(10deg);
  }
}
@keyframes airTree {
  0%, 100% {
    -webkit-transform: rotate(0deg) translateX(0);
    transform: rotate(0deg) translateX(0);
  }
  25%, 75% {
    -webkit-transform: rotate(5deg) translateX(15px);
    transform: rotate(5deg) translateX(15px);
  }
  50% {
    -webkit-transform: rotate(10deg) translateX(30px);
    transform: rotate(10deg) translateX(30px);
  }
}
@keyframes airTree2 {
  0%, 100% {
    transform: rotate(0deg) translateX(0);
  }
  25%, 75% {
    transform: rotate(5deg) translateX(5px);
  }
  50% {
    transform: rotate(10deg) translateX(10px);
  }
}
@keyframes float-bob-y-2 {
  0% {
    -webkit-transform: translateY(0px) translateX(0px) rotate(0deg);
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  50% {
    -webkit-transform: translateY(10px) translateX(10px) rotate(5deg);
    transform: translateY(10px) translateX(10px) rotate(5deg);
  }
  100% {
    -webkit-transform: translateY(0px) translateX(0px) rotate(0deg);
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
}
/*--------------------------------------------------------------
# Mobile Nav
--------------------------------------------------------------*/
.mobile-nav__wrapper {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 999;
  transform: translateX(-100%);
  transform-origin: left center;
  transition: transform 500ms ease 500ms, visibility 500ms ease 500ms;
  visibility: hidden;
  position: fixed;
}
.mobile-nav__wrapper .container {
  padding-left: 0;
  padding-right: 0;
}
.mobile-nav__wrapper .home-showcase .row [class*=col-] {
  flex: 0 0 100%;
}
.mobile-nav__wrapper .home-showcase {
  margin-bottom: -1px;
  margin-top: 0;
  border-bottom: 1px solid RGBA(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}
.mobile-nav__wrapper .home-showcase__inner {
  padding: 15px 0px;
  background-color: transparent;
  box-shadow: none;
}
.mobile-nav__wrapper .home-showcase__title {
  color: var(--grdeen-white, #fff);
}

.mobile-nav__wrapper.expanded {
  opacity: 1;
  transform: translateX(0%);
  visibility: visible;
  transition: transform 500ms ease 0ms, visibility 500ms ease 0ms;
}
.mobile-nav__wrapper.expanded .mobile-nav__content {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  transition: opacity 500ms ease 500ms, visibility 500ms ease 500ms, transform 500ms ease 500ms;
}

.mobile-nav__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--grdeen-black, #172000);
  opacity: 0.5;
  cursor: url(../images/close.png), auto;
}

.mobile-nav__content {
  width: 300px;
  background-color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.9);
  z-index: 10;
  position: relative;
  height: 100%;
  overflow-y: auto;
  padding-top: 30px;
  padding-bottom: 30px;
  padding-left: 15px;
  padding-right: 15px;
  opacity: 0;
  visibility: hidden;
  transform: translateX(-100%);
  transition: opacity 500ms ease 0ms, visibility 500ms ease 0ms, transform 500ms ease 0ms;
}
.mobile-nav__content .main-menu__nav {
  display: block;
  padding: 0;
}

.mobile-nav__content .logo-box {
  margin-bottom: 40px;
  display: flex;
}

.mobile-nav__close {
  position: absolute;
  top: 20px;
  right: 15px;
  font-size: 18px;
  color: var(--grdeen-white, #fff);
  cursor: pointer;
}
.mobile-nav__close:hover {
  color: var(--grdeen-base, #1a9120);
}

.mobile-nav__content .main-menu__list,
.mobile-nav__content .main-menu__list ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

.mobile-nav__content .main-menu__list ul {
  display: none;
  border-top: 1px solid RGBA(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}

.mobile-nav__content .main-menu__list ul li > a {
  padding-left: 1em;
}

.mobile-nav__content .main-menu__list li:not(:last-child) {
  border-bottom: 1px solid RGBA(var(--grdeen-white-rgb, 255, 255, 255), 0.1);
}

.mobile-nav__content .main-menu__list li > a {
  display: flex;
  justify-content: space-between;
  line-height: 30px;
  color: var(--grdeen-white, #fff);
  font-size: 12px;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  text-transform: uppercase;
  font-weight: 500;
  height: 46px;
  align-items: center;
  transition: 500ms;
}

.mobile-nav__content .main-menu__list li a.expanded {
  color: var(--grdeen-base, #1a9120);
}

.mobile-nav__content .main-menu__list li a button {
  width: 30px;
  height: 30px;
  background-color: var(--grdeen-base, #1a9120);
  border: none;
  outline: none;
  color: var(--grdeen-white, #fff);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  transform: rotate(-90deg);
  transition: transform 500ms ease;
}

.mobile-nav__content .main-menu__list li a button.expanded {
  transform: rotate(0deg);
  background-color: var(--grdeen-white, #fff);
  color: var(--grdeen-black, #172000);
}

.mobile-nav__social {
  display: flex;
  align-items: center;
}
.mobile-nav__social a {
  font-size: 16px;
  color: var(--grdeen-white, #fff);
  transition: 500ms;
}
.mobile-nav__social a + a {
  margin-left: 20px;
}
.mobile-nav__social a:hover {
  color: var(--grdeen-base, #1a9120);
}

.mobile-nav__contact {
  margin-bottom: 0;
  margin-top: 20px;
  margin-bottom: 20px;
}
.mobile-nav__contact li {
  color: var(--grdeen-white, #fff);
  font-size: 14px;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
}
.mobile-nav__contact li + li {
  margin-top: 15px;
}
.mobile-nav__contact li a {
  color: inherit;
  transition: 500ms;
}
.mobile-nav__contact li a:hover {
  color: var(--grdeen-base, #1a9120);
}
.mobile-nav__contact li > i {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  font-size: 12px;
  margin-right: 10px;
  color: var(--grdeen-white, #fff);
}

.mobile-nav__container .main-menu__logo,
.mobile-nav__container .main-menu__right {
  display: none;
}

/*--------------------------------------------------------------
# Search Popup
--------------------------------------------------------------*/
.search-popup {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -2;
  -webkit-transition: all 1s ease;
  -khtml-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -ms-transition: all 1s ease;
  -o-transition: all 1s ease;
  transition: all 1s ease;
}
.search-popup__overlay {
  position: fixed;
  width: 224vw;
  height: 224vw;
  top: calc(90px - 112vw);
  right: calc(50% - 112vw);
  z-index: 3;
  display: block;
  -webkit-border-radius: 50%;
  -khtml-border-radius: 50%;
  -moz-border-radius: 50%;
  -ms-border-radius: 50%;
  -o-border-radius: 50%;
  border-radius: 50%;
  -webkit-transform: scale(0);
  -khtml-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
  -webkit-transform-origin: center;
  transform-origin: center;
  -webkit-transition: transform 0.8s ease-in-out;
  -khtml-transition: transform 0.8s ease-in-out;
  -moz-transition: transform 0.8s ease-in-out;
  -ms-transition: transform 0.8s ease-in-out;
  -o-transition: transform 0.8s ease-in-out;
  transition: transform 0.8s ease-in-out;
  transition-delay: 0s;
  transition-delay: 0.3s;
  -webkit-transition-delay: 0.3s;
  background-color: #000;
  opacity: 0.9;
  cursor: url(../images/close.png), auto;
}
@media (max-width: 767px) {
  .search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: translateY(-110%);
  }
}
.search-popup__content {
  position: fixed;
  width: 0;
  max-width: 560px;
  padding: 30px 15px;
  left: 50%;
  top: 50%;
  opacity: 0;
  z-index: 3;
  -webkit-transform: translate(-50%, -50%);
  -khtml-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  -webkit-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -khtml-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -moz-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -ms-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  -o-transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  transition: opacity 0.5s 0s, width 0.8s 0.8s cubic-bezier(0.225, 0.01, 0.475, 1.01), transform 0.2s 0s;
  transition-delay: 0s, 0.8s, 0s;
  transition-delay: 0s, 0.4s, 0s;
  transition-delay: 0.2s;
  -webkit-transition-delay: 0.2s;
}
.search-popup__form {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}
.search-popup__form input[type=search],
.search-popup__form input[type=text] {
  width: 100%;
  background-color: var(--grdeen-white, #fff);
  font-size: 15px;
  color: var(--grdeen-text, #626f62);
  border: none;
  outline: none;
  height: 66px;
  padding-left: 30px;
}
.search-popup__form .grdeen-btn {
  padding: 0;
  width: 66px;
  height: 66px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: absolute;
  top: 0;
  right: -1px;
  border-radius: 0;
  font-size: 20px;
}
.search-popup__form .grdeen-btn i {
  margin: 0;
}
.search-popup__form .grdeen-btn::before {
  left: -12px;
  width: 150%;
}
.search-popup.active {
  z-index: 9999;
}
.search-popup.active .search-popup__overlay {
  top: auto;
  bottom: calc(90px - 112vw);
  -webkit-transform: scale(1);
  -khtml-transform: scale(1);
  -moz-transform: scale(1);
  -ms-transform: scale(1);
  -o-transform: scale(1);
  transform: scale(1);
  transition-delay: 0s;
  -webkit-transition-delay: 0s;
  opacity: 0.9;
  -webkit-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -khtml-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -moz-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -ms-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  -o-transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
  transition: transform 1.6s cubic-bezier(0.4, 0, 0, 1);
}
@media (max-width: 767px) {
  .search-popup.active .search-popup__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transform: none;
    width: 100%;
    height: 100%;
    border-radius: 0;
    transform: translateY(0%);
  }
}
.search-popup.active .search-popup__content {
  width: 100%;
  opacity: 1;
  transition-delay: 0.7s;
  -webkit-transition-delay: 0.7s;
}

/*--------------------------------------------------------------
# Page Header
--------------------------------------------------------------*/
.page-header {
  background-color: var(--grdeen-black3, #545454);
  position: relative;
  padding-top: 238px;
  padding-bottom: 170px;
  margin-top: -101px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .page-header {
    padding-top: 180px;
    padding-bottom: 80px;
  }
}
.page-header__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url(../images/backgrounds/pageheader.jpg);
  mix-blend-mode: overlay;
}
.page-header__shape {
  position: absolute;
  position: absolute;
  left: 0;
  bottom: -166px;
  width: 44%;
  height: 100%;
  background-position: right bottom;
  background-repeat: no-repeat;
  background-image: url(../images/shapes/page-header-bg-shape.png);
  mix-blend-mode: hard-light;
}
.page-header__overlay {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--grdeen-black2, #4f5345);
  mix-blend-mode: overlay;
}
.page-header .container {
  position: relative;
  z-index: 10;
  text-align: center;
}
.page-header__title {
  margin: 0;
  font-weight: 700;
  font-size: 50px;
  color: var(--grdeen-white, #fff);
  margin-bottom: 12px;
}
@media (max-width: 767px) {
  .page-header__title {
    font-size: 40px;
  }
}

.grdeen-breadcrumb {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  margin: 0;
}
.grdeen-breadcrumb li {
  font-size: 14px;
  color: var(--grdeen-white, #fff);
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-weight: 600;
  display: flex;
  align-items: center;
}
.grdeen-breadcrumb li:not(:last-of-type)::after {
  content: "/";
  position: relative;
  top: 0;
  margin-left: 4px;
  margin-right: 4px;
}
.grdeen-breadcrumb li span,
.grdeen-breadcrumb li a {
  color: inherit;
  display: inline-flex;
  line-height: 1.1;
}
.grdeen-breadcrumb li a {
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.grdeen-breadcrumb li a:hover {
  background-size: 100% 1px;
}

/*--------------------------------------------------------------
# Google Map
--------------------------------------------------------------*/
.google-map {
  position: relative;
}
.google-map iframe {
  position: relative;
  display: block;
  border: none;
  height: 570px;
  width: 100%;
  mix-blend-mode: luminosity;
}
@media (max-width: 767px) {
  .google-map iframe {
    height: 450px;
  }
}
.google-map__contact {
  overflow: hidden;
  background-color: var(--grdeen-gray, #f6f7f2);
}

.contact-map {
  position: relative;
}

/*--------------------------------------------------------------
# Client Carousel
--------------------------------------------------------------*/
.client-carousel {
  background-color: var(--grdeen-base, #1a9120);
  background-image: url(../images/shapes/client-carousel-bg-1-1.html);
  background-size: cover;
  padding: 80px 0;
}
@media (min-width: 992px) {
  .client-carousel {
    padding: 102px 0;
  }
}
.client-carousel__one__item img {
  opacity: 0.2;
  transition: all 500ms ease;
  max-width: 100%;
  width: auto !important;
}
.client-carousel__one__item:hover img {
  opacity: 0.6;
}

.client-carousel-one {
  position: relative;
  background: var(--grdeen-white, #fff);
  padding: 0 0 60px;
}
.client-carousel-one .client-carousel__one {
  padding: 74px 0 0;
}
.client-carousel-one .owl-theme .owl-nav {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  margin: auto;
  z-index: 2;
  line-height: 0.8;
}
.client-carousel-one .owl-theme .owl-nav button {
  width: 26px;
  height: 27px;
  background-color: var(--grdeen-gray, #f6f7f2);
  margin: 0 2.5px;
  padding: 0;
  transition: all 500ms ease;
  font-size: 12px;
  color: var(--grdeen-black, #172000);
  text-align: center;
  font-size: 12px;
  line-height: 27px;
  border-radius: 0;
}
.client-carousel-one .owl-theme .owl-nav button:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.client-carousel-one .owl-theme .owl-nav::before {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  width: 100%;
  content: "";
  height: 1px;
  background-color: var(--grdeen-border-color, #e7e7e7);
  z-index: -1;
}
.client-carousel-one .owl-theme .owl-nav::after {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: -1;
  width: 110px;
  content: "";
  height: 1px;
  background-color: var(--grdeen-white, #fff);
}
.client-carousel-one .client-carousel__one__item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 103px;
  transition: all 500ms ease;
}
.client-carousel-one .client-carousel__one__item img {
  transition: all 500ms ease;
  opacity: 0.2;
  max-width: 100%;
  width: auto;
}
.client-carousel-one .client-carousel__one__item:hover {
  background-color: #f8f5f0;
}
.client-carousel-one .client-carousel__one__item:hover img {
  opacity: 0.6;
}

/*--------------------------------------------------------------
# Hero Slider
--------------------------------------------------------------*/
.main-slider-one {
  position: relative;
  margin-top: -80px;
}
.main-slider-one__carousel {
  position: relative;
  width: 100%;
}
.main-slider-one__carousel.owl-carousel .owl-nav {
  left: 5.6%;
  margin: auto 0;
  position: absolute;
  right: 0;
  text-align: left;
  top: 52%;
  transform: translateY(-50%);
  max-width: 90px;
}
@media (max-width: 1600px) {
  .main-slider-one__carousel.owl-carousel .owl-nav {
    left: 40px;
  }
}
@media (max-width: 1400px) {
  .main-slider-one__carousel.owl-carousel .owl-nav {
    left: 0;
    right: 0;
    left: 0;
    bottom: 35px;
    top: initial;
    margin: 0 auto;
    gap: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: initial;
    transform: initial;
  }
}
.main-slider-one__carousel.owl-carousel .owl-nav button {
  transition: all 400ms ease;
  width: 68px;
  height: 73px;
  outline: none;
  box-shadow: none;
  border: none;
  background-color: var(--grdeen-white, #fff);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 27px;
  margin: 0;
  padding: 0;
  text-align: center;
}
@media (max-width: 767px) {
  .main-slider-one__carousel.owl-carousel .owl-nav button {
    width: 58px;
    height: 63px;
    font-size: 20px;
  }
}
.main-slider-one__carousel.owl-carousel .owl-nav button:last-child {
  margin-top: 30px;
}
@media (max-width: 1400px) {
  .main-slider-one__carousel.owl-carousel .owl-nav button:last-child {
    margin: 0;
  }
}
.main-slider-one__carousel.owl-carousel .owl-nav button:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.main-slider-one__carousel .owl-dots {
  right: 118px;
  margin: auto 0;
  position: absolute;
  right: 0;
  text-align: left;
  top: 50%;
  transform: translateY(-50%);
  width: 140px;
}
@media (max-width: 1400px) {
  .main-slider-one__carousel .owl-dots {
    width: 60px;
  }
}
@media (max-width: 1199px) {
  .main-slider-one__carousel .owl-dots {
    width: 40px;
  }
}
@media (max-width: 767px) {
  .main-slider-one__carousel .owl-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    margin: auto;
    left: 0;
    top: 68%;
  }
}
.main-slider-one__carousel .owl-dots .owl-dot {
  display: block;
  margin: 6px 0;
}
@media (max-width: 767px) {
  .main-slider-one__carousel .owl-dots .owl-dot {
    margin: 0 6px;
  }
}
.main-slider-one__carousel .owl-dots .owl-dot span {
  width: 16px;
  height: 16px;
  display: block;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid var(--grdeen-white, #fff);
  margin: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-slider-one__carousel .owl-dots .owl-dot:hover span,
.main-slider-one__carousel .owl-dots .owl-dot.active span {
  background-color: var(--grdeen-white, #fff);
}
.main-slider-one__item {
  background-color: var(--grdeen-black3, #545454);
  position: relative;
  z-index: 3;
  padding-top: 234px;
  padding-bottom: 158px;
}
@media (max-width: 767px) {
  .main-slider-one__item {
    padding-top: 150px;
    padding-bottom: 150px;
  }
}
.main-slider-one__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: var(--grdeen-black3, #545454);
  mix-blend-mode: overlay;
}
.main-slider-one__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--grdeen-black2, #4f5345);
  mix-blend-mode: overlay;
}
.main-slider-one__shape {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 19.1%;
  height: 100%;
  height: auto;
  mix-blend-mode: hard-light;
  opacity: 0;
  transform: translateY(200px);
  transition: all 1000ms ease;
}
.main-slider-one__shape img {
  width: 100% !important;
}
.main-slider-one__content {
  position: relative;
  display: inline-block;
  z-index: 3;
  overflow: hidden;
  padding-top: 12px;
}
.main-slider-one__sub-title {
  text-align: left;
  color: var(--grdeen-white, #fff);
  font-size: 15px;
  font-weight: 600;
  text-transform: uppercase;
  line-height: 21px;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
  margin-bottom: 21px;
}
.main-slider-one__sub-title img {
  width: 32px !important;
  height: 30px;
  top: -4px;
  position: relative;
  display: inline-block !important;
  margin-right: 3px;
  animation: airTree2 5s ease-in infinite;
}
.main-slider-one__title {
  font-weight: 700;
  color: var(--grdeen-white, #fff);
  font-size: 90px;
  line-height: 92px;
  display: inline-block;
  overflow: hidden;
  margin: 0 0 22px;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateX(200px);
}
@media (max-width: 991px) {
  .main-slider-one__title {
    font-size: 85px;
    line-height: 88px;
  }
}
@media (max-width: 767px) {
  .main-slider-one__title {
    font-size: 48px;
    line-height: 50px;
  }
}
.main-slider-one__title::after {
  content: "";
  width: 101%;
  height: 100%;
  position: absolute;
  top: 2px;
  right: 100%;
  background: var(--grdeen-white, #fff);
  transition: 1s cubic-bezier(0.858, 0.01, 0.068, 0.99);
  z-index: 3;
  transform: translateX(100%);
  transition-delay: 1s;
}
@media (max-width: 991px) {
  .main-slider-one__title::after {
    top: 0px;
  }
}
@media (max-width: 767px) {
  .main-slider-one__title::after {
    top: 2px;
  }
}
.main-slider-one__btn {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 5;
  overflow: hidden;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
}
.main-slider-one__btn .grdeen-btn {
  padding: 19px 55px;
  font-size: 15px;
  font-weight: 500;
}
@media (max-width: 767px) {
  .main-slider-one__btn .grdeen-btn {
    padding: 15px 45px;
    font-size: 13px;
  }
}
.main-slider-one__btn .grdeen-btn::before {
  background-color: var(--grdeen-black, #172000);
}
.main-slider-one .active .main-slider-one__sub-title {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1100ms;
}
.main-slider-one .active .main-slider-one__title {
  opacity: 1;
  transform: translateX(0);
  transition-delay: 1300ms;
}
.main-slider-one .active .main-slider-one__title::after {
  transform: translateX(0%);
  transition-delay: 1500ms;
}
.main-slider-one .active .main-slider-one__btn {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1700ms;
}
.main-slider-one .active .main-slider-one__shape {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 1000ms;
}

.main-slider-two {
  margin-top: -101px;
  position: relative;
}
.main-slider-two__carousel {
  position: relative;
  width: 100%;
}
.main-slider-two__carousel.owl-carousel .owl-nav {
  left: inherit;
  margin: auto 0;
  position: absolute;
  right: 50px;
  text-align: left;
  top: 51%;
  transform: translateY(-50%);
  max-width: 90px;
}
@media (max-width: 1600px) {
  .main-slider-two__carousel.owl-carousel .owl-nav {
    right: 30px;
  }
}
@media (max-width: 1400px) {
  .main-slider-two__carousel.owl-carousel .owl-nav {
    left: 0;
    right: 0;
    bottom: 35px;
    top: initial;
    margin: 0 auto;
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    max-width: initial;
    transform: initial;
  }
}
@media (max-width: 1200px) {
  .main-slider-two__carousel.owl-carousel .owl-nav {
    right: 20px;
  }
}
@media (max-width: 767px) {
  .main-slider-two__carousel.owl-carousel .owl-nav {
    display: none;
  }
}
.main-slider-two__carousel.owl-carousel .owl-nav button {
  transition: all 400ms ease;
  width: 68px;
  height: 73px;
  outline: none;
  box-shadow: none;
  border: none;
  background-color: var(--grdeen-white, #fff);
  border-radius: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 27px;
  margin: 0;
  padding: 0;
  text-align: center;
}
@media (max-width: 767px) {
  .main-slider-two__carousel.owl-carousel .owl-nav button {
    width: 58px;
    height: 63px;
    font-size: 20px;
  }
}
.main-slider-two__carousel.owl-carousel .owl-nav button:last-child {
  margin-top: 30px;
}
@media (max-width: 1400px) {
  .main-slider-two__carousel.owl-carousel .owl-nav button:last-child {
    margin: 0;
  }
}
.main-slider-two__carousel.owl-carousel .owl-nav button:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.main-slider-two__carousel .owl-dots {
  right: 118px;
  margin: auto 0;
  position: absolute;
  right: 0;
  text-align: left;
  top: 50%;
  transform: translateY(-50%);
  width: 140px;
}
@media (max-width: 1400px) {
  .main-slider-two__carousel .owl-dots {
    width: 60px;
  }
}
@media (max-width: 1199px) {
  .main-slider-two__carousel .owl-dots {
    width: 40px;
  }
}
@media (max-width: 767px) {
  .main-slider-two__carousel .owl-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    width: auto;
    margin: auto;
    left: 0;
    top: 68%;
  }
}
.main-slider-two__carousel .owl-dots .owl-dot {
  display: block;
  margin: 6px 0;
}
@media (max-width: 767px) {
  .main-slider-two__carousel .owl-dots .owl-dot {
    margin: 0 6px;
  }
}
.main-slider-two__carousel .owl-dots .owl-dot span {
  width: 16px;
  height: 16px;
  display: block;
  border-radius: 50%;
  background-color: transparent;
  border: 2px solid var(--grdeen-white, #fff);
  margin: 0;
  -webkit-transition: all 300ms ease;
  transition: all 300ms ease;
}
.main-slider-two__carousel .owl-dots .owl-dot:hover span,
.main-slider-two__carousel .owl-dots .owl-dot.active span {
  background-color: var(--grdeen-white, #fff);
}
.main-slider-two__item {
  background-color: var(--grdeen-black3, #545454);
  position: relative;
  z-index: 3;
  padding-top: 297px;
  padding-bottom: 223px;
}
@media (max-width: 1199px) {
  .main-slider-two__item {
    padding-top: 220px;
  }
}
@media (max-width: 767px) {
  .main-slider-two__item {
    padding-top: 160px;
    padding-bottom: 80px;
  }
}
.main-slider-two__bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  background-color: var(--grdeen-black3, #545454);
  mix-blend-mode: overlay;
}
.main-slider-two__bg__color {
  background-color: var(--grdeen-black, #172000);
  width: 16.66%;
  height: 100%;
  display: block;
  position: absolute;
  transition: all 500ms ease;
  z-index: -1;
}
.main-slider-two__bg__color:nth-child(2), .main-slider-two__bg__color:nth-child(4), .main-slider-two__bg__color:nth-child(6) {
  top: 0;
}
.main-slider-two__bg__color:nth-child(2) {
  left: 16.66%;
}
.main-slider-two__bg__color:nth-child(4) {
  left: 49.98%;
}
.main-slider-two__bg__color:nth-child(6) {
  left: 83.3%;
}
.main-slider-two__bg__color:nth-child(1), .main-slider-two__bg__color:nth-child(3), .main-slider-two__bg__color:nth-child(5) {
  bottom: 0;
}
.main-slider-two__bg__color:nth-child(1) {
  left: 0;
}
.main-slider-two__bg__color:nth-child(3) {
  left: 33.32%;
}
.main-slider-two__bg__color:nth-child(5) {
  left: 66.64%;
}
.main-slider-two__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--grdeen-black2, #4f5345);
  mix-blend-mode: overlay;
}
.main-slider-two__shape {
  position: absolute;
  left: 0;
  bottom: -8%;
  width: 44%;
  height: auto;
  mix-blend-mode: hard-light;
  opacity: 0;
  transform: translateY(100%);
  transition: all 1000ms ease;
}
.main-slider-two__shape img {
  width: 100%;
}
.main-slider-two__content {
  position: relative;
  display: inline-block;
  z-index: 3;
  overflow: hidden;
  padding-top: 12px;
}
.main-slider-two__sub-title {
  color: var(--grdeen-white, #fff);
  font-size: 15px;
  font-weight: 600;
  line-height: 21px;
  text-transform: uppercase;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
  margin-bottom: 15px;
}
.main-slider-two__sub-title img {
  width: 32px !important;
  height: 30px;
  top: -4px;
  position: relative;
  display: inline-block !important;
  margin-right: 3px;
  animation: airTree2 5s ease-in infinite;
}
.main-slider-two__title {
  font-weight: 700;
  color: var(--grdeen-white, #fff);
  font-size: 90px;
  line-height: 92px;
  display: inline-block;
  overflow: hidden;
  margin: 0 0 15px;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateX(200px);
}
@media (max-width: 1199px) {
  .main-slider-two__title {
    font-size: 80px;
  }
}
@media (max-width: 991px) {
  .main-slider-two__title {
    font-size: 80px;
    line-height: 88px;
  }
}
@media (max-width: 767px) {
  .main-slider-two__title {
    font-size: 45px;
    line-height: 50px;
  }
}
.main-slider-two__title::after {
  content: "";
  width: 101%;
  height: 100%;
  position: absolute;
  top: 2px;
  right: 100%;
  background: var(--grdeen-white, #fff);
  transition: 1s cubic-bezier(0.858, 0.01, 0.068, 0.99);
  z-index: 3;
  transform: translateX(100%);
  transition-delay: 1s;
}
@media (max-width: 991px) {
  .main-slider-two__title::after {
    top: 0px;
  }
}
.main-slider-two__subtext {
  color: var(--grdeen-white, #fff);
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
  margin-bottom: 36px;
  opacity: 0;
  transform: perspective(400px) rotateX(0deg) translateX(-600px);
  transform-origin: left;
  transition: all 1500ms ease;
}
@media (max-width: 767px) {
  .main-slider-two__subtext {
    font-size: 18px;
    line-height: 28px;
  }
}
.main-slider-two__btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
  overflow: hidden;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
}
.main-slider-two__btn .grdeen-btn {
  font-size: 15px;
  font-weight: 500;
  padding: 19px 55px;
  height: 60px;
}
.main-slider-two__btn .grdeen-btn::before {
  background-color: var(--grdeen-black, #172000);
}
@media (max-width: 767px) {
  .main-slider-two__btn .grdeen-btn {
    padding: 15px 35px;
    font-size: 13px;
    height: auto;
  }
}
.main-slider-two .active .main-slider-two__bg__color {
  height: 0;
}
.main-slider-two .active .main-slider-two__sub-title {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1200ms;
}
.main-slider-two .active .main-slider-two__title {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1400ms;
}
.main-slider-two .active .main-slider-two__title::after {
  transform: translateY(1%);
  transition-delay: 1600ms;
}
.main-slider-two .active .main-slider-two__subtext {
  opacity: 1;
  transform: perspective(400px) rotateX(0deg) translateX(0px);
  transition-delay: 1800ms;
}
.main-slider-two .active .main-slider-two__btn {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1900ms;
}
.main-slider-two .active .main-slider-two__shape {
  opacity: 1;
  transform: translateY(0px);
  transition-delay: 1200ms;
}

.main-slider-three {
  position: relative;
  overflow: hidden;
  margin-top: -101px;
}
.main-slider-three__carousel {
  position: relative;
  width: 100%;
}
.main-slider-three__carousel.owl-carousel .owl-nav {
  left: inherit;
  margin: auto 0;
  position: absolute;
  right: 50px;
  text-align: left;
  top: 55%;
  transform: translateY(-50%);
  max-width: 90px;
}
@media (max-width: 1600px) {
  .main-slider-three__carousel.owl-carousel .owl-nav {
    right: 30px;
  }
}
@media (max-width: 1400px) {
  .main-slider-three__carousel.owl-carousel .owl-nav {
    left: 0;
    right: 0;
    bottom: 35px;
    top: initial;
    margin: 0 auto;
    display: flex;
    gap: 20px;
    align-items: center;
    justify-content: center;
    max-width: initial;
    transform: initial;
  }
}
@media (max-width: 1200px) {
  .main-slider-three__carousel.owl-carousel .owl-nav {
    right: 20px;
  }
}
@media (max-width: 767px) {
  .main-slider-three__carousel.owl-carousel .owl-nav {
    display: none;
  }
}
.main-slider-three__carousel.owl-carousel .owl-nav button {
  transition: all 400ms ease;
  width: 68px;
  height: 73px;
  outline: none;
  box-shadow: none;
  border: none;
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.08);
  border-radius: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 27px;
  margin: 0 0;
  text-align: center;
}
@media (max-width: 767px) {
  .main-slider-three__carousel.owl-carousel .owl-nav button {
    width: 58px;
    height: 63px;
    font-size: 20px;
  }
}
.main-slider-three__carousel.owl-carousel .owl-nav button:last-child {
  margin-top: 30px;
}
@media (max-width: 1400px) {
  .main-slider-three__carousel.owl-carousel .owl-nav button:last-child {
    margin: 0;
  }
}
.main-slider-three__carousel.owl-carousel .owl-nav button:hover {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
}
.main-slider-three__item {
  background-color: var(--grdeen-gray, #f6f7f2);
  position: relative;
  z-index: 3;
  padding-top: 319px;
  padding-bottom: 206px;
}
@media (max-width: 1400px) {
  .main-slider-three__item {
    padding-top: 250px;
  }
}
@media (max-width: 767px) {
  .main-slider-three__item {
    padding-top: 180px;
    padding-bottom: 86px;
  }
}
@media (max-width: 767px) {
  .main-slider-three__item {
    padding-top: 180px;
    padding-bottom: 86px;
  }
}
@media (max-width: 1199px) {
  .main-slider-three__item .col-lg-6 {
    width: 100%;
  }
}
.main-slider-three__bg2, .main-slider-three__bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  background-repeat: repeat;
  background-position: center top;
  background-color: #e9f4ea;
  background-blend-mode: overlay;
}
.main-slider-three__bg2 img, .main-slider-three__bg img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 0.5s;
  background-size: cover;
}
.main-slider-three__bg2 img:nth-child(1), .main-slider-three__bg img:nth-child(1) {
  transform: translatex(50%) scalex(2);
  opacity: 0;
  filter: blur(10px);
}
.main-slider-three__bg2 img:nth-child(2), .main-slider-three__bg img:nth-child(2) {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  object-fit: cover;
}
.main-slider-three__bg2 {
  width: 100%;
  top: initial;
  right: initial;
  background-repeat: repeat-x;
  background-color: transparent;
  background-blend-mode: initial;
  background-position: left top;
  height: 115px;
  animation: sliderEffect3Grass 80s linear 0s infinite;
}
@keyframes sliderEffect3Grass {
  0% {
    background-position: -1920px top;
  }
  100% {
    background-position: 0px top;
  }
}
@media (max-width: 991px) {
  .main-slider-three__bg2 {
    height: 80px;
  }
}
.main-slider-three__shapeimg {
  position: absolute;
  width: 100%;
  height: 100%;
  max-width: 47.7%;
  left: 0;
  top: -1px;
  z-index: 2;
  transition: transform 1400ms ease, opacity 1000ms ease;
  opacity: 0;
  transform: translate(-10%, -10%);
}
@media (max-width: 1199px) {
  .main-slider-three__shapeimg {
    display: none;
  }
}
.main-slider-three__shapeimg__inner {
  overflow: hidden;
  width: calc(100% - 38px);
  -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.000000 296.000000"><g transform="translate(0.000000,296.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M0 1514 l0 -1447 78 6 c103 7 184 34 388 127 262 120 362 148 479 131 90 -13 157 -44 298 -137 225 -148 322 -184 496 -184 172 0 307 45 439 145 118 89 218 241 258 395 20 76 22 282 5 425 -16 133 -14 330 4 400 29 113 62 161 215 320 163 168 196 211 250 328 96 211 112 452 43 671 -25 81 -77 190 -113 236 l-19 25 -1410 3 -1411 2 0 -1446z"/></g></svg>');
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.000000 296.000000"><g transform="translate(0.000000,296.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M0 1514 l0 -1447 78 6 c103 7 184 34 388 127 262 120 362 148 479 131 90 -13 157 -44 298 -137 225 -148 322 -184 496 -184 172 0 307 45 439 145 118 89 218 241 258 395 20 76 22 282 5 425 -16 133 -14 330 4 400 29 113 62 161 215 320 163 168 196 211 250 328 96 211 112 452 43 671 -25 81 -77 190 -113 236 l-19 25 -1410 3 -1411 2 0 -1446z"/></g></svg>');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: right bottom;
  mask-position: right bottom;
  -webkit-mask-size: cover;
  mask-size: cover;
  position: relative;
  z-index: 2;
}
.main-slider-three__shapeimg__inner img {
  width: 100%;
}
@media (min-width: 1200px) and (max-width: 1500px) {
  .main-slider-three__shapeimg__inner img {
    min-height: 720px;
    object-fit: cover;
  }
}
.main-slider-three__shapeimg__shape3, .main-slider-three__shapeimg__shape2 {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: 1;
}
@media (min-width: 1200px) and (max-width: 1500px) {
  .main-slider-three__shapeimg__shape3 img, .main-slider-three__shapeimg__shape2 img {
    min-height: 800px;
  }
}
.main-slider-three__shapeimg__shape3 {
  width: 125px;
  height: auto;
  right: 3%;
  bottom: -25%;
  left: auto;
  margin: auto 0;
  display: flex;
  align-items: center;
}
@media (max-width: 1500px) {
  .main-slider-three__shapeimg__shape3 {
    display: none;
  }
}
.main-slider-three__content {
  position: relative;
  display: block;
  z-index: 3;
  overflow: hidden;
  padding-top: 12px;
}
@media (min-width: 1300px) {
  .main-slider-three__content {
    margin-right: -50px;
  }
}
@media (min-width: 1200px) {
  .main-slider-three__content {
    margin-left: -30px;
  }
}
.main-slider-three__sub-title {
  color: var(--grdeen-black, #172000);
  font-size: 15px;
  font-weight: 600;
  line-height: 21px;
  text-transform: uppercase;
  transition: transform 1000ms ease, opacity 1000ms ease;
  transform: translateY(-200px);
  margin-bottom: 3px;
}
.main-slider-three__sub-title img {
  width: 32px !important;
  height: 30px;
  top: -4px;
  position: relative;
  display: inline-block !important;
  margin-right: 3px;
  animation: airTree2 5s ease-in infinite;
}
.main-slider-three__title {
  font-weight: 700;
  color: var(--grdeen-black, #172000);
  font-size: 90px;
  line-height: 92px;
  display: inline-block;
  overflow: hidden;
  margin: 0 0 28px;
  opacity: 0;
  transition: transform 1200ms ease, opacity 1200ms ease;
  transform: translateX(200px);
}
@media (max-width: 1300px) {
  .main-slider-three__title {
    font-size: 80px;
    line-height: 85px;
  }
}
@media (max-width: 767px) {
  .main-slider-three__title {
    font-size: 45px;
    line-height: 50px;
    margin-bottom: 15px;
  }
}
.main-slider-three__title::after {
  content: "";
  width: 101%;
  height: 100%;
  position: absolute;
  top: 2px;
  right: 100%;
  background: var(--grdeen-white, #fff);
  transition: 1s cubic-bezier(0.858, 0.01, 0.068, 0.99);
  z-index: 3;
  transform: translateX(100%);
  transition-delay: 1s;
}
@media (max-width: 991px) {
  .main-slider-three__title::after {
    top: 0px;
  }
}
.main-slider-three__subtext {
  color: var(--grdeen-text, #626f62);
  font-size: 20px;
  font-weight: 500;
  line-height: 30px;
  margin-bottom: 36px;
  opacity: 0;
  transform: perspective(400px) rotateX(0deg) translateX(-600px);
  transform-origin: left;
  transition: all 1500ms ease;
}
@media (min-width: 1200px) and (max-width: 1300px) {
  .main-slider-three__subtext br {
    display: none;
  }
}
@media (max-width: 767px) {
  .main-slider-three__subtext {
    font-size: 18px;
    margin-bottom: 30px;
  }
}
.main-slider-three__btn {
  position: relative;
  display: flex;
  align-items: center;
  z-index: 5;
  overflow: hidden;
  opacity: 0;
  transform: perspective(400px) rotateY(0deg) translateY(80px);
  transform-origin: bottom;
  transition: all 1500ms ease;
  margin-left: 5px;
}
@media (max-width: 640px) {
  .main-slider-three__btn {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px 0;
  }
}
.main-slider-three__btn .grdeen-btn {
  padding: 19px 55px;
  font-size: 15px;
  font-weight: 500;
  height: 60px;
}
.main-slider-three__btn .grdeen-btn::before {
  background-color: var(--grdeen-black, #172000);
}
.main-slider-three__btn .grdeen-btn + .grdeen-btn {
  margin-left: 10px;
  background-color: var(--grdeen-white, #fff);
  color: var(--grdeen-text-dark2, #0e150e);
  padding: 19px 45px;
}
.main-slider-three__btn .grdeen-btn + .grdeen-btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.main-slider-three__btn .grdeen-btn + .grdeen-btn:hover {
  color: var(--grdeen-white, #fff);
}
@media (max-width: 640px) {
  .main-slider-three__btn .grdeen-btn + .grdeen-btn {
    margin-left: 0;
  }
}
.main-slider-three__shape5, .main-slider-three__shape4 {
  position: absolute;
  right: 0;
  bottom: -20%;
  width: 400px;
  height: auto;
  z-index: 2;
  transition: all 0.5s ease;
}
.main-slider-three__shape5 img, .main-slider-three__shape4 img {
  width: 100%;
}
@media (max-width: 991px) {
  .main-slider-three__shape4 {
    display: none;
  }
}
.main-slider-three__shape5 {
  width: 212px;
  right: -10%;
  top: 12%;
  bottom: 0;
  margin: 0 auto;
  display: flex;
  align-items: center;
  transition: all 0.5s ease;
}
@media (max-width: 1400px) {
  .main-slider-three__shape5 {
    display: none;
  }
}
.main-slider-three .active .main-slider-three__bg img:nth-child(1) {
  transform: translatex(0) scalex(1);
  opacity: 1;
  filter: blur(0);
}
.main-slider-three .active .main-slider-three__bg img:nth-child(2) {
  transform: translatex(-50%) scalex(2);
  opacity: 0;
  filter: blur(10px);
}
.main-slider-three .active .main-slider-three__sub-title {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1100ms;
}
.main-slider-three .active .main-slider-three__title {
  opacity: 1;
  transform: translateY(0);
  transition-delay: 1300ms;
}
.main-slider-three .active .main-slider-three__title::after {
  transform: translateY(1%);
  transition-delay: 1600ms;
}
.main-slider-three .active .main-slider-three__subtext {
  opacity: 1;
  transform: perspective(400px) rotateX(0deg) translateX(0px);
  transition-delay: 1800ms;
}
.main-slider-three .active .main-slider-three__btn {
  opacity: 1;
  transform: perspective(400px) rotateY(0deg) translateY(0px);
  transition-delay: 1800ms;
}
.main-slider-three .active .main-slider-three__shape4 {
  opacity: 1;
  bottom: 0;
  transition-delay: 1000ms;
}
.main-slider-three .active .main-slider-three__shape5 {
  opacity: 1;
  right: 0;
  transition-delay: 1000ms;
}
.main-slider-three .active .main-slider-three__shapeimg {
  opacity: 1;
  transition-delay: 1600ms;
  transform: translate(0, 0);
}

/*--------------------------------------------------------------
# Feature Section
--------------------------------------------------------------*/
.feature-one {
  position: relative;
  z-index: 2;
  background-color: var(--grdeen-gray2, #f1f4f1);
  padding: 9px 0 39px;
}
.feature-one .container-fluid {
  max-width: 1770px;
  margin: 0 auto;
}
.feature-one__row {
  width: auto;
  margin: 0 -35px;
}
.feature-one__item {
  display: flex;
  align-items: center;
  padding: 0 20px;
}
@media (max-width: 991px) {
  .feature-one__item {
    width: 100%;
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .feature-one__item {
    flex-direction: column-reverse;
  }
}
.feature-one__item__content {
  padding-right: 29px;
}
@media (max-width: 767px) {
  .feature-one__item__content {
    padding-right: 0;
  }
}
.feature-one__item__title {
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  margin-bottom: 9px;
}
@media (max-width: 767px) {
  .feature-one__item__title {
    text-align: center;
  }
}
.feature-one__item__text {
  color: var(--grdeen-text, #626f62);
  font-weight: 400;
  font-size: 15px;
  line-height: 28px;
  margin-bottom: 0;
}
@media (max-width: 767px) {
  .feature-one__item__text {
    text-align: center;
  }
}
.feature-one__item__iconwrap {
  width: 102px;
  min-width: 102px;
  height: 102px;
  border: 1px solid var(--grdeen-text-dark, #07370a);
  background-color: var(--grdeen-white, #fff);
  border-radius: 50%;
  transition: all 0.5s ease;
}
@media (max-width: 767px) {
  .feature-one__item__iconwrap {
    margin-bottom: 10px;
  }
}
.feature-one__item__icon {
  color: var(--grdeen-text-dark, #07370a);
  font-size: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  transition: all 0.5s ease;
}
.feature-one__item__icon svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
.feature-one__item:hover .feature-one__item__iconwrap {
  background-color: var(--grdeen-text-dark, #07370a);
}
.feature-one__item:hover .feature-one__item__icon {
  transform: scale(0.9);
  color: var(--grdeen-white, #fff);
}
.feature-one--two {
  background-color: var(--grdeen-white, #fff);
  padding: 120px 0;
  padding-top: 90px;
}
@media (max-width: 767px) {
  .feature-one--two {
    padding: 80px 0;
    padding-top: 50px;
  }
}
.feature-one--two .feature-one__row {
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.feature-one--two .container {
  max-width: 1600px;
}
.feature-one--two .feature-one__item {
  position: relative;
  background-color: var(--grdeen-gray2, #f1f4f1);
  flex-direction: column-reverse;
  padding: 42px 30px 52px;
  border-radius: 4px;
  overflow: hidden;
}
@media (min-width: 1400px) {
  .feature-one--two .feature-one__item {
    padding-left: 50px;
    padding-right: 50px;
  }
}
.feature-one--two .feature-one__item__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  bottom: -12px;
  z-index: 1;
  background-repeat: repeat-x;
  background-position: left bottom;
}
.feature-one--two .feature-one__item__content,
.feature-one--two .feature-one__item__iconwrap {
  position: relative;
  z-index: 2;
}
.feature-one--two .feature-one__item__content {
  padding-right: 0;
  padding-bottom: 25px;
  text-align: center;
  position: relative;
}
.feature-one--two .feature-one__item__content::after {
  content: "";
  position: absolute;
  width: 100%;
  max-width: 55px;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0 auto;
  border-bottom: 2px dashed var(--grdeen-base, #1a9120);
}
.feature-one--two .feature-one__item__iconwrap {
  margin-bottom: 20px;
  border: 0;
}
.feature-one--three {
  background-color: transparent;
  padding: 0;
}
.feature-one--three .container-fluid {
  max-width: 1600px;
}
.feature-one--three .feature-one__item {
  padding: 0;
  overflow: hidden;
  background-color: var(--grdeen-gray2, #f1f4f1);
  position: relative;
  border-bottom: 2px dashed var(--grdeen-base, #1a9120);
  height: 100%;
  border-radius: 4px;
}
.feature-one--three .feature-one__item__innerwrap {
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding: 34px 20px 41px 40px;
}
@media (min-width: 1200px) and (max-width: 1400px) {
  .feature-one--three .feature-one__item__innerwrap {
    padding-left: 25px;
    padding-right: 15px;
  }
}
@media (max-width: 991px) {
  .feature-one--three .feature-one__item__innerwrap {
    padding: 20px;
  }
}
.feature-one--three .feature-one__item__bgshape, .feature-one--three .feature-one__item__bg {
  position: absolute;
  width: 100%;
  height: 50px;
  left: 0;
  bottom: -9px;
  z-index: 1;
  background-repeat: repeat-x;
  background-position: left bottom;
}
.feature-one--three .feature-one__item__bgshape {
  height: 100%;
  background-repeat: no-repeat;
  background-position: right top;
  bottom: initial;
  top: 0;
  transition: all 0.5s ease;
}
.feature-one--three .feature-one__item__iconwrap {
  width: auto;
  min-width: initial;
  height: auto;
  border: 0;
  background-color: transparent;
  border-radius: 0;
}
.feature-one--three .feature-one__item__icon {
  width: 61px;
  font-size: 60px;
}
.feature-one--three .feature-one__item__content {
  padding-right: 70px;
  position: relative;
  display: flex;
  align-items: center;
  width: calc((100% - 61px) / 1);
}
.feature-one--three .feature-one__item__title {
  padding-left: 21px;
  margin-bottom: 0;
  text-align: left;
}
@media (min-width: 1200px) and (max-width: 1400px) {
  .feature-one--three .feature-one__item__title {
    padding-left: 15px;
  }
}
.feature-one--three .feature-one__item .service-one__item__iconlink {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto 0;
  right: 0;
  width: 58px;
  height: 58px;
  border-radius: 50%;
  background-color: var(--grdeen-white, #fff);
}
.feature-one--three .feature-one__item__text {
  width: 100%;
  margin-top: 17px;
}
.feature-one--three .feature-one__item:hover .feature-one__item__iconwrap {
  background-color: transparent;
}
.feature-one--three .feature-one__item:hover .feature-one__item__icon {
  transform: scale(0.95);
  color: var(--grdeen-text-dark, #07370a);
}
.feature-one--three .feature-one__item:hover .service-one__item__iconlink {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  transform: scaleY(-1);
}
.feature-one--three .feature-one__item:hover .feature-one__item__bgshape {
  transform: translate(12px, -12px);
  filter: brightness(0) invert(0);
}

/*--------------------------------------------------------------
# About
--------------------------------------------------------------*/
.about-one {
  padding: 120px 0;
  padding-bottom: 115px;
  position: relative;
  overflow: hidden;
}
@media (max-width: 767px) {
  .about-one {
    padding: 80px 0;
  }
}
@media (max-width: 1200px) {
  .about-one .container {
    max-width: 100%;
  }
}
.about-one__image {
  position: relative;
  z-index: 2;
  padding-right: 48px;
  padding-bottom: 90px;
}
@media (max-width: 991px) {
  .about-one__image {
    padding-right: 0;
    margin-bottom: 44px;
  }
}
.about-one__bigimage {
  width: 100%;
  border-radius: 4px;
}
.about-one__smimage {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  right: 16px;
  z-index: 1;
  display: flex;
  justify-content: flex-end;
  align-items: flex-end;
  padding: 200px 0 0 200px;
}
@media (max-width: 767px) {
  .about-one__smimage {
    padding: 160px 0 0 140px;
  }
}
.about-one__smimage img {
  width: 100%;
  max-width: 334px;
  border: 8px solid var(--grdeen-white, #fff);
  border-radius: 12px;
}
.about-one__shapetop, .about-one__shapebottom {
  content: "";
  position: absolute;
  width: 22px;
  z-index: 2;
  background-color: var(--grdeen-base, #1a9120);
}
.about-one__shapetop {
  background-color: var(--grdeen-text-dark, #07370a);
  height: 138px;
  top: 0;
  right: 10px;
  animation: aboutShapetop 1.8s linear infinite alternate;
}
@keyframes aboutShapetop {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}
@media (max-width: 991px) {
  .about-one__shapetop {
    right: 6px;
  }
}
@media (max-width: 767px) {
  .about-one__shapetop {
    height: 106px;
  }
}
.about-one__shapebottom {
  height: 163px;
  bottom: 5px;
  left: 190px;
  animation: aboutShapeleft 1.2s linear infinite alternate;
}
@keyframes aboutShapeleft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-150%);
  }
}
@media (max-width: 1200px) {
  .about-one__shapebottom {
    left: 150px;
  }
}
@media (max-width: 767px) {
  .about-one__shapebottom {
    left: 80px;
  }
}
.about-one .sec-title {
  padding-bottom: 30px;
}
.about-one__content {
  position: relative;
  margin-top: -14px;
}
@media (min-width: 1200px) {
  .about-one__content {
    padding-left: 0;
  }
}
.about-one__content__text {
  font-size: 16px;
  line-height: 30px;
  margin: 0 0 32px;
}
.about-one__content__qualitwrap {
  position: relative;
  background-color: rgba(var(--grdeen-gray2-rgb, 241, 244, 241), 0.85);
  border-radius: 4px;
  padding: 15px 220px 13px 20px;
  margin-bottom: 32px;
}
@media (max-width: 520px) {
  .about-one__content__qualitwrap {
    padding-right: 20px;
    padding-bottom: 18px;
  }
}
.about-one__content__qualitwrap::before {
  content: "";
  position: absolute;
  width: 3px;
  height: 100%;
  max-height: 120px;
  top: 0;
  left: -1px;
  bottom: 0;
  margin: auto 0;
  z-index: 1;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  align-items: center;
}
.about-one__content__qualitwrap__title {
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  margin-bottom: 11px;
}
.about-one__content__qualitwrap .qualit-text {
  font-size: 15px;
  line-height: 28px;
  margin-bottom: 0;
}
.about-one__content__qualitwrap__pricesbox {
  position: absolute;
  width: auto;
  right: 0;
  top: 0;
  height: 100%;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  padding: 15px 20px;
}
@media (max-width: 520px) {
  .about-one__content__qualitwrap__pricesbox {
    position: static;
    margin-top: 16px;
  }
}
.about-one__content__qualitwrap__price {
  font-weight: 600;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-size: 46px;
  line-height: 1;
  color: var(--grdeen-white, #fff);
  display: block;
  margin-bottom: 13px;
}
.about-one__content__qualitwrap__text {
  font-weight: 600;
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-size: 16px;
  line-height: 1;
  color: var(--grdeen-white, #fff);
  display: block;
}
.about-one__content__list {
  margin: -8px 0 0 0;
  padding: 0;
}
.about-one__content__list li {
  line-height: 32px;
  position: relative;
}
.about-one__content__list li i {
  margin-right: 9px;
}
.about-one__content__list li + li {
  margin-top: 4px;
}
.about-one--two {
  padding-top: 0;
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .about-one--two {
    padding-bottom: 80px;
  }
}
.about-one--two .about-one__image {
  padding-right: 46px;
  padding-bottom: 60px;
}
.about-one--two .about-one__shapetop {
  right: 8px;
}
.about-one--two .about-one__shapebottom {
  height: 22px;
  width: 184px;
  left: 0;
  bottom: 12px;
  animation: aboutShapeleft2 1s linear infinite alternate;
}
@keyframes aboutShapeleft2 {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(20%);
  }
}
@media (max-width: 767px) {
  .about-one--two .about-one__shapebottom {
    width: 106px;
  }
}
.about-one--two .about-one__smimage {
  right: -2px;
}
@media (max-width: 767px) {
  .about-one--two .about-one__smimage {
    padding-left: 140px;
  }
}
.about-one--two .about-one__content {
  margin-top: -6px;
}
@media (min-width: 1200px) {
  .about-one--two .about-one__content {
    padding-left: 40px;
  }
}
@media (max-width: 991px) {
  .about-one--two .about-one__content {
    padding-left: 0;
  }
}
.about-one--two .about-one__content__qualitwrap {
  background-color: transparent;
  padding: 0 205px 0 0;
}
@media (max-width: 767px) {
  .about-one--two .about-one__content__qualitwrap {
    padding: 0;
  }
}
.about-one--two .about-one__content__qualitwrap::before {
  content: none;
}
.about-one--two .about-one__content__qualitwrap__col {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}
.about-one--two .about-one__content__qualitwrap__col:hover .about-one__content__qualitwrap__icon {
  background-color: var(--grdeen-base, #1a9120);
}
.about-one--two .about-one__content__qualitwrap__icon {
  width: 64px;
  min-width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: var(--grdeen-text-dark, #07370a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 37px;
  color: var(--grdeen-white, #fff);
  transition: all 0.5s ease;
}
.about-one--two .about-one__content__qualitwrap__icon svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
.about-one--two .about-one__content__qualitwrap__title {
  margin-bottom: 0;
  padding-left: 16px;
}
@media (max-width: 991px) {
  .about-one--two .about-one__content__qualitwrap__title {
    padding-left: 12px;
  }
  .about-one--two .about-one__content__qualitwrap__title br {
    display: none;
  }
}
.about-one--two .about-one__content__qualitwrap__pricesbox {
  background-color: var(--grdeen-text-dark, #07370a);
  border-radius: 0 47px 47px 47px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .about-one--two .about-one__content__qualitwrap__pricesbox {
    width: 100%;
    position: relative;
    margin-top: 38px;
  }
}
.about-one--two .about-one__content__qualitwrap__tpright, .about-one--two .about-one__content__qualitwrap__btmleft {
  color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.3);
  font-size: 38px;
  position: absolute;
  z-index: 1;
}
.about-one--two .about-one__content__qualitwrap__tpright {
  right: 9px;
  top: 21px;
}
.about-one--two .about-one__content__qualitwrap__btmleft {
  font-size: 102px;
  left: -10px;
  bottom: -50px;
}
.about-one--two .grdeen-btn {
  margin-top: 35px;
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-text-dark, #07370a);
  padding: 18px 38px;
}
.about-one--two .grdeen-btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.about-one--two--about {
  position: relative;
  padding: 120px 0 0;
}
@media (max-width: 767px) {
  .about-one--two--about {
    padding: 80px 0 0;
  }
}
.about-one--three {
  padding: 120px 0;
  position: relative;
}
@media (max-width: 767px) {
  .about-one--three {
    padding: 80px 0;
  }
}
.about-one--three .about-one__content {
  position: relative;
  max-width: 100%;
  margin: 0;
}
@media (min-width: 1200px) {
  .about-one--three .about-one__content {
    padding: 0 48px 0 0;
  }
}
@media (max-width: 991px) {
  .about-one--three .about-one__content {
    margin-bottom: 36px;
  }
}
.about-one--three .about-one__content__list {
  margin: -2px 0 0;
}
.about-one--three .about-one__image {
  padding: 12px 0 0 3px;
  margin: 0;
}
@media (min-width: 1400px) {
  .about-one--three .about-one__image {
    margin-left: 80px;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .about-one--three .about-one__image {
    margin-left: 70px;
  }
}
.about-one--three .about-one__bigimage {
  -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.000000 309.000000"><g transform="translate(0.000000,309.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M867 3080 c-84 -21 -135 -67 -206 -187 -23 -39 -74 -101 -113 -139 -162 -155 -179 -179 -203 -269 -19 -75 -19 -222 0 -385 36 -301 12 -396 -167 -664 -97 -144 -132 -217 -152 -318 -22 -109 -21 -261 3 -337 40 -130 101 -169 311 -201 209 -32 247 -54 348 -203 116 -170 168 -207 302 -215 101 -6 196 19 377 97 182 78 241 94 338 95 124 1 204 -37 400 -193 50 -40 124 -89 164 -109 73 -36 77 -37 190 -37 110 0 119 2 182 32 169 83 264 276 226 459 -8 38 -45 136 -81 217 -80 178 -90 216 -84 315 6 98 30 153 143 322 114 171 138 224 143 326 3 50 0 96 -8 119 -19 58 -73 111 -138 136 -54 20 -67 21 -274 14 -191 -6 -222 -5 -268 10 -107 36 -170 142 -155 262 8 69 30 129 93 268 63 136 76 188 70 273 -9 117 -79 221 -184 273 -41 20 -64 24 -139 24 -120 0 -167 -23 -305 -151 -156 -144 -255 -183 -348 -135 -44 23 -83 63 -146 153 -50 72 -91 108 -147 132 -48 20 -126 27 -172 16z"/></g></svg>');
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300.000000 309.000000"><g transform="translate(0.000000,309.000000) scale(0.100000,-0.100000)" stroke="none"><path d="M867 3080 c-84 -21 -135 -67 -206 -187 -23 -39 -74 -101 -113 -139 -162 -155 -179 -179 -203 -269 -19 -75 -19 -222 0 -385 36 -301 12 -396 -167 -664 -97 -144 -132 -217 -152 -318 -22 -109 -21 -261 3 -337 40 -130 101 -169 311 -201 209 -32 247 -54 348 -203 116 -170 168 -207 302 -215 101 -6 196 19 377 97 182 78 241 94 338 95 124 1 204 -37 400 -193 50 -40 124 -89 164 -109 73 -36 77 -37 190 -37 110 0 119 2 182 32 169 83 264 276 226 459 -8 38 -45 136 -81 217 -80 178 -90 216 -84 315 6 98 30 153 143 322 114 171 138 224 143 326 3 50 0 96 -8 119 -19 58 -73 111 -138 136 -54 20 -67 21 -274 14 -191 -6 -222 -5 -268 10 -107 36 -170 142 -155 262 8 69 30 129 93 268 63 136 76 188 70 273 -9 117 -79 221 -184 273 -41 20 -64 24 -139 24 -120 0 -167 -23 -305 -151 -156 -144 -255 -183 -348 -135 -44 23 -83 63 -146 153 -50 72 -91 108 -147 132 -48 20 -126 27 -172 16z"/></g></svg>');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: center center;
  mask-position: center center;
  -webkit-mask-size: cover;
  mask-size: cover;
  position: relative;
  z-index: 2;
  width: auto;
  max-width: 100%;
  border-radius: 0;
}
.about-one--three .about-one__bigimagebg {
  position: absolute;
  width: auto;
  max-width: 100%;
  border-radius: 0;
  top: 0;
  left: 0;
  z-index: 1;
}
.about-one--three .about-one__smimage {
  right: auto;
  left: -50px;
  width: 328px;
  height: 328px;
  border: 6px solid var(--grdeen-white, #fff);
  overflow: hidden;
  top: 10%;
  bottom: 0;
  margin: auto 0;
  padding: 0;
  z-index: 3;
  border-radius: 50%;
  animation: about3Effect 1.5s linear infinite alternate;
}
@media (min-width: 1400px) {
  .about-one--three .about-one__smimage {
    left: -67px;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .about-one--three .about-one__smimage {
    position: relative;
    left: 0;
    top: -10%;
  }
}
@media (max-width: 767px) {
  .about-one--three .about-one__smimage {
    display: none;
  }
}
@keyframes about3Effect {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(15px);
  }
}
.about-one--three .about-one__smimage img {
  object-fit: cover;
  border-radius: inherit;
  width: 100%;
  height: 100%;
  border: none;
}

/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/
.service-one {
  position: relative;
  background-color: #edf5ee;
  padding: 112px 0;
  padding-bottom: 120px;
  counter-reset: count;
}
.service-one__bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: overlay;
}
@media (max-width: 767px) {
  .service-one {
    padding: 80px 0;
  }
}
.service-one .container {
  position: relative;
  z-index: 2;
}
@media (max-width: 1200px) {
  .service-one .container {
    max-width: 100%;
  }
}
.service-one .sec-title {
  text-align: center;
  padding-bottom: 52px;
}
.service-one .sec-title__title {
  margin-top: 1px;
}
.service-one__item {
  background-color: var(--grdeen-white, #fff);
  counter-increment: count;
  border-radius: 8px;
  padding: 31px 21px 17px 32px;
  transition: all 0.5s ease;
}
.service-one__item:hover {
  box-shadow: 0px 10px 60px 0px rgba(0, 0, 0, 0.05);
}
.service-one__item__titlewrap {
  padding-right: 5px;
  padding-bottom: 28px;
}
.service-one__item__titlecol {
  width: calc((100% - 74px) / 1);
  padding-right: 10px;
}
.service-one__item__count {
  font-weight: 600;
  color: rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.2);
  font-family: var(--grdeen-heading-font, "DM Sans", sans-serif);
  font-size: 38px;
  line-height: 1;
  position: relative;
  display: inline-block;
  padding-left: 28px;
  margin-bottom: 5px;
  transition: all 0.5s ease;
}
.service-one__item__count::before {
  content: counters(count, ".", decimal-leading-zero);
  position: relative;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-one__item__count::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 4px;
  top: 0;
  bottom: 0;
  left: 0;
  margin: auto 0;
  background-color: var(--grdeen-green, #1f9a26);
  transition: all 0.5s ease;
}
.service-one__item__title {
  font-weight: 600;
  color: var(--grdeen-text-dark, #07370a);
  font-size: 24px;
  line-height: 31px;
  margin-bottom: 0;
}
.service-one__item__title:hover {
  color: var(--grdeen-base, #1a9120);
}
.service-one__item__title a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.service-one__item__title a:hover {
  background-size: 100% 1px;
}
.service-one__item__iconwrap {
  width: 74px;
  height: 74px;
  border-radius: 50%;
  background-color: var(--grdeen-text-dark, #07370a);
  box-shadow: 0px 0px 0px 4px rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.2);
  transition: all 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-one__item__icon {
  color: var(--grdeen-white, #fff);
  font-size: 43px;
  transition: all 0.5s ease;
}
.service-one__item__icon svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
.service-one__item__text {
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 0;
}
.service-one__item__btmwrap {
  border-top: 1px solid rgba(var(--grdeen-text-rgb, 98, 111, 98), 0.2);
  margin-top: 27px;
  padding-top: 19px;
}
.service-one__item__iconlink {
  width: 47px;
  height: 47px;
  border-radius: 50%;
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.16);
  font-size: 24px;
  color: var(--grdeen-black, #172000);
  transition: all 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-one__item__link {
  color: var(--grdeen-black, #172000);
  font-weight: 600;
  font-size: 13px;
  line-height: 1.2;
  text-transform: uppercase;
  position: relative;
  transition: all 0.5s ease;
}
.service-one__item__link::after {
  content: "";
  position: absolute;
  width: 100%;
  max-width: 100%;
  height: 1px;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  margin: 0 auto;
  background-color: var(--grdeen-black, #172000);
  transition: all 0.5s ease;
}
.service-one__item__link:hover {
  color: var(--grdeen-base, #1a9120);
}
.service-one__item__link:hover::after {
  max-width: 70%;
  background-color: var(--grdeen-base, #1a9120);
}
.service-one__item:hover .service-one__item__count {
  color: rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.8);
}
.service-one__item:hover .service-one__item__count::after {
  left: 5px;
}
.service-one__item:hover .service-one__item__iconwrap {
  background-color: var(--grdeen-base, #1a9120);
  -webkit-box-shadow: 0px 0px 0px 5px rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.8);
  box-shadow: 0px 0px 0px 5px rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.8);
}
.service-one__item:hover .service-one__item__iconlink {
  background-color: var(--grdeen-base, #1a9120);
  color: var(--grdeen-white, #fff);
  transform: scaleY(-1);
}
.service-one--two {
  background-color: #0e3010;
}
.service-one--two .container {
  max-width: 1600px;
}
.service-one--two.service-one--page {
  background-color: #edf5ee;
}
.service-one--two.service-one--page .container {
  max-width: 1200px;
}
.service-one--two .sec-title__img, .service-one--two .sec-title__tagline, .service-one--two .sec-title__title {
  color: var(--grdeen-white, #fff);
}
.service-one--two .service-one__item {
  padding: 0;
}
.service-one--two .service-one__item__image {
  width: 100%;
  position: relative;
  z-index: 1;
  overflow: hidden;
}
.service-one--two .service-one__item__image::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: 1;
  transform: translate(70%, 70%);
  background-color: rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.5);
  transition: all 0.6s ease;
  opacity: 0;
}
.service-one--two .service-one__item__image img {
  width: 100% !important;
  border-radius: 8px 8px 0 0;
}
.service-one--two .service-one__item:hover .service-one__item__image::before {
  transform: translate(0%, 0%);
  opacity: 1;
}
.service-one--two .service-one__item__content {
  padding: 0 23px 20px 28px;
  position: relative;
  z-index: 2;
}
.service-one--two .service-one__item .service-one__item__titlewrap {
  flex-direction: column-reverse;
  align-items: initial !important;
  padding-right: 12px;
  padding-bottom: 14px;
}
.service-one--two .service-one__item .service-one__item__titlecol {
  width: calc((100% - 0px) / 1);
  padding-right: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: row-reverse;
}
.service-one--two .service-one__item .service-one__item__iconwrap {
  margin-top: -40px;
  margin-bottom: 12px;
}
.service-one--two .service-one__item .service-one__item__count {
  margin-bottom: 0;
}
.service-one--three {
  background-color: transparent;
}
.service-one--three .service-one__item {
  background-color: var(--grdeen-gray2, #f1f4f1);
  border-radius: 8px;
  padding: 0;
  overflow: hidden;
  position: relative;
}
.service-one--three .service-one__item__bgeffect {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  z-index: 1;
  background-repeat: no-repeat;
  background-position: right top;
  background-size: auto;
  transition: all 0.5s ease;
}
.service-one--three .service-one__item__titlewrap {
  position: relative;
  z-index: 2;
  width: 100%;
  padding: 34px 26px 67px 26px;
}
.service-one--three .service-one__item__titlecol {
  width: 100%;
  padding: 0;
  text-align: center;
}
.service-one--three .service-one__item__title {
  margin-bottom: 17px;
}
.service-one--three .service-one__item__text {
  margin-bottom: 17px;
}
.service-one--three .service-one__item__image {
  position: relative;
  z-index: 2;
}
.service-one--three .service-one__item__image img {
  width: 100%;
}
.service-one--three .service-one__item__iconwrap, .service-one--three .service-one__item__btmwrap {
  position: absolute;
  z-index: 1;
}
.service-one--three .service-one__item__iconwrap {
  top: -36px;
  right: 0;
  left: 0;
  margin: 0 auto;
  background-color: var(--grdeen-base, #1a9120);
  -webkit-box-shadow: 0px 0px 0px 4px #d3dad4;
  box-shadow: 0px 0px 0px 4px #d3dad4;
}
.service-one--three .service-one__item__btmwrap {
  margin: 0;
  padding: 0;
  border: 0;
  right: 19px;
  bottom: 28px;
}
.service-one--three .service-one__item__iconlink {
  background-color: var(--grdeen-white, #fff);
}
.service-one--three .service-one__item:hover .service-one__item__bgeffect {
  transform: translate(18px, -32px);
  filter: brightness(0) invert(0) opacity(0.3);
}
.service-one--three .service-one__item:hover .service-one__item__iconwrap,
.service-one--three .service-one__item:hover .service-one__item__iconlink {
  background-color: var(--grdeen-text-dark, #07370a);
}
.service-one--three .service-one__item:hover .service-one__item__iconwrap {
  -webkit-box-shadow: 0px 0px 0px 6px var(--grdeen-base, #1a9120);
  box-shadow: 0px 0px 0px 6px var(--grdeen-base, #1a9120);
}
.service-one--page {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .service-one--page {
    padding: 80px 0;
  }
}

/*--------------------------------------------------------------
# Service details
--------------------------------------------------------------*/
.service-details {
  position: relative;
  padding: 120px 0;
  counter-reset: count;
}
@media (max-width: 767px) {
  .service-details {
    padding: 80px 0;
  }
}
.service-details__content {
  position: relative;
}
@media (min-width: 1200px) {
  .service-details__content {
    padding-left: 30px;
  }
}
.service-details__thumbnail {
  margin-bottom: 21px;
}
.service-details__thumbnail img {
  width: 100%;
  border-radius: 5px;
}
.service-details__title {
  margin: 0;
  color: var(--grdeen-black, #172000);
  font-size: 30px;
  font-weight: 700;
  font-family: var(--grdeen-font, "Inter", sans-serif);
  margin-bottom: 12px;
}
@media (min-width: 992px) {
  .service-details__title {
    font-size: 42px;
  }
}
.service-details__heading {
  margin: 36px 0 14px;
  color: var(--grdeen-black, #172000);
  font-weight: 600;
  font-size: 24px;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.service-details__text {
  font-size: 16px;
  line-height: 30px;
  margin: 0 0 16px;
}
.service-details__box-wrapper {
  margin-top: -14px;
  margin-bottom: 25px;
}
.service-details__box {
  position: relative;
  min-height: 76px;
  padding: 0 0 0 94px;
}
.service-details__box__icon {
  width: 74px;
  height: 76px;
  background-color: var(--grdeen-base, #1a9120);
  font-size: 45px;
  color: var(--grdeen-white, #fff);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  transition: all 0.5s ease;
}
.service-details__box:hover .service-details__box__icon {
  background-color: var(--grdeen-text-dark, #07370a);
}
.service-details__box__title {
  margin: 0 0 8px;
  font-size: 20px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.service-details__box__text {
  margin: 0;
  font-size: 15px;
  line-height: 28px;
}
.service-details__benefit {
  position: relative;
  margin-top: 27px;
  margin-bottom: 45px;
}
.service-details__benefit img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}
.service-details__benefit__content {
  position: relative;
}
@media (min-width: 1200px) {
  .service-details__benefit__content {
    padding-left: 5px;
  }
}
.service-details__benefit__title {
  margin: -5px 0 15px;
  font-size: 24px;
  font-weight: 600;
  font-family: var(--grdeen-font, "Inter", sans-serif);
}
.service-details__benefit__text {
  margin: 0;
  font-size: 15px;
  line-height: 28px;
}
.service-details__benefit__list {
  margin: 20px 0 10px;
  padding: 0;
  list-style: none;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
@media (max-width: 767px) {
  .service-details__benefit__list {
    display: block;
  }
}
.service-details__benefit__list li {
  position: relative;
  font-size: 18px;
  line-height: 32px;
  font-weight: 500;
  padding-left: 31px;
  color: var(--grdeen-base, #1a9120);
  margin-bottom: 8px;
}
.service-details__benefit__list__icon {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  line-height: 1;
  font-size: 22px;
  display: inline-block;
}
.service-details__accordion {
  background-color: var(--grdeen-gray2, #f1f4f1);
  border-radius: 5px;
  padding: 44px 25px;
  margin-top: 22px;
}
.service-details__accordion .accrodion {
  position: relative;
  margin-bottom: 11px;
  counter-increment: count;
}
.service-details__accordion .accrodion-title {
  padding: 17px 55px 17px 28px;
  position: relative;
  cursor: pointer;
  background-color: var(--grdeen-white, #fff);
}
.service-details__accordion .accrodion-title h4 {
  font-weight: 500;
  color: var(--grdeen-black, #172000);
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-size: 18px;
  margin: 0;
  transition: all 500ms ease;
  position: relative;
}
.service-details__accordion .accrodion-title__number {
  position: relative;
  display: inline-block;
}
.service-details__accordion .accrodion-title__number::before {
  content: counters(count, ".", decimal);
  position: relative;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.service-details__accordion .accrodion-title__icon {
  width: 28px;
  height: 28px;
  position: absolute;
  top: 50%;
  right: -30px;
  transform: translateY(-50%);
  border-radius: 50%;
  background-color: #d3d3d2;
  transition: all 500ms ease;
}
.service-details__accordion .accrodion-title__icon::after, .service-details__accordion .accrodion-title__icon::before {
  width: 1px;
  height: 15px;
  position: absolute;
  background-color: var(--grdeen-white, #fff);
  top: 50%;
  left: 50%;
  content: "";
  transform: translate(-50%, -50%);
  transition: all 500ms ease;
}
.service-details__accordion .accrodion-title__icon::after {
  width: 15px;
  height: 1px;
}
.service-details__accordion .active .accrodion-title__icon {
  background-color: var(--grdeen-base, #1a9120);
}
.service-details__accordion .active .accrodion-title__icon::after, .service-details__accordion .active .accrodion-title__icon::before {
  background-color: var(--grdeen-white, #fff);
  opacity: 0;
}
.service-details__accordion .active .accrodion-title__icon::after {
  opacity: 1;
  width: 13px;
  height: 2px;
}
.service-details__accordion .accrodion-content .inner {
  padding: 20px 25px 12px;
}
.service-details__accordion .accrodion-content p {
  margin: 0;
  font-size: 16px;
  line-height: 30px;
}

/*--------------------------------------------------------------
# great-together
--------------------------------------------------------------*/
.great-together {
  position: relative;
  z-index: 2;
  margin-bottom: -140px;
}
@media (max-width: 1200px) {
  .great-together .container {
    max-width: 100%;
  }
}
@media (max-width: 991px) {
  .great-together .container {
    padding: 0;
  }
}
.great-together__wrapper {
  position: relative;
  padding: 46px 24px 56px 24px;
  border-radius: 4px;
  overflow: hidden;
}
@media (max-width: 767px) {
  .great-together__wrapper {
    padding-top: 32px;
    padding-bottom: 40px;
  }
}
.great-together__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  mix-blend-mode: overlay;
  background-size: cover;
}
.great-together__overlay {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #0a4a0d;
}
.great-together__content {
  position: relative;
  z-index: 2;
}
.great-together__title {
  color: var(--grdeen-white, #fff);
  font-weight: 600;
  font-size: 40px;
  line-height: 48px;
  margin-bottom: 9px;
}
@media (max-width: 767px) {
  .great-together__title {
    font-size: 32px;
    line-height: 34px;
  }
}
.great-together__text {
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.8);
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 30px;
}
.great-together__btn {
  color: var(--grdeen-text-dark, #07370a);
  border: 2px solid var(--grdeen-black, #172000);
  border-radius: 4px;
  background-color: var(--grdeen-white, #fff);
  padding-left: 42px;
  padding-right: 42px;
}
.great-together__btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.great-together__btn:hover {
  border-color: var(--grdeen-white, #fff);
}

/*--------------------------------------------------------------
# Boxed Home
--------------------------------------------------------------*/
body.boxed-wrapper {
  position: relative;
}
body.boxed-wrapper .page-wrapper {
  max-width: 1530px;
  margin-left: auto;
  margin-right: auto;
  background-color: var(--grdeen-white, #fff);
  box-shadow: 0px 0px 100px 0px rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.08);
}
body.boxed-wrapper .main-slider-one__carousel.owl-carousel .owl-nav {
  left: 2.5%;
}
body.boxed-wrapper .main-header__wellcome__tagline {
  display: none;
}

/*--------------------------------------------------------------
# Work Process
--------------------------------------------------------------*/
.work-process-one {
  position: relative;
  counter-reset: count;
  padding: 112px 0;
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .work-process-one {
    padding: 80px 0;
  }
}
@media (max-width: 1200px) {
  .work-process-one .container {
    max-width: 100%;
  }
}
.work-process-one .sec-title {
  padding-bottom: 47px;
}
@media (max-width: 991px) {
  .work-process-one .sec-title {
    padding-bottom: 0px;
  }
}
.work-process-one .sec-title__title {
  margin-top: 6px;
}
.work-process-one__infowrap {
  padding-top: 37px;
  padding-left: 76px;
}
@media (max-width: 991px) {
  .work-process-one__infowrap {
    padding-left: 0;
    padding-top: 0;
    padding-bottom: 35px;
  }
}
.work-process-one__infowrap__text {
  font-size: 16px;
  line-height: 28px;
  margin-bottom: 0;
}
.work-process-one__col {
  position: relative;
  counter-increment: count;
  background-color: var(--grdeen-white2, #f2f4ec);
  border-radius: 8px;
  padding: 41px 20px 25px 20px;
  overflow: hidden;
}
.work-process-one__col__circlewrap {
  width: 144px;
  height: 144px;
  border: 2px dashed var(--grdeen-green, #1f9a26);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
  transition: all 0.5s ease;
}
.work-process-one__col__circlewrap::before {
  content: counters(count, "", decimal-leading-zero);
  position: absolute;
  bottom: 30px;
  left: -26px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 41px;
  height: 41px;
  border-radius: 50%;
  background-color: var(--grdeen-green, #1f9a26);
  color: var(--grdeen-white, #fff);
  font-weight: 600;
  font-size: 16px;
  line-height: 1;
  transition: all 0.5s ease;
}
.work-process-one__col__icon {
  width: 105px;
  height: 105px;
  border-radius: 50%;
  background-color: var(--grdeen-text-dark, #07370a);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 56px;
  color: var(--grdeen-white, #fff);
  -webkit-box-shadow: 0px 0px 0px 8px var(--grdeen-white, #fff);
  box-shadow: 0px 0px 0px 8px var(--grdeen-white, #fff);
  transition: all 0.5s ease;
}
.work-process-one__col__info {
  position: relative;
  z-index: 2;
}
.work-process-one__col__title {
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 700;
  font-size: 24px;
  line-height: 31px;
  margin-bottom: 12px;
}
.work-process-one__col__text {
  font-size: 15px;
  line-height: 28px;
  margin-bottom: 0;
}
.work-process-one__col__thumb {
  display: inline-block;
}
.work-process-one__col__thumb__number {
  width: 67px;
  height: 67px;
}
.work-process-one__col__shapebg {
  position: absolute;
  width: 100%;
  height: 100%;
  max-height: 270px;
  left: 0;
  bottom: 0;
  z-index: 1;
  -webkit-clip-path: polygon(100% 0, 0 100%, 100% 100%);
  clip-path: polygon(100% 0, 0 100%, 100% 100%);
  background-color: rgba(var(--grdeen-black-rgb, 23, 32, 0), 0.03);
  transition: all 0.5s ease;
}
.work-process-one__col:hover .work-process-one__col__icon {
  background-color: var(--grdeen-green, #1f9a26);
  -webkit-box-shadow: 0px 0px 0px 9px rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.8);
  box-shadow: 0px 0px 0px 9px rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.8);
}
.work-process-one__col:hover .work-process-one__col__circlewrap::before {
  background-color: rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.9);
}
.work-process-one__col:hover .work-process-one__col__shapebg {
  transform: translate(15%, 15%);
  background-color: rgba(var(--grdeen-base-rgb, 26, 145, 32), 0.09);
}
.work-process-one--three {
  background-color: #09290b;
  overflow: hidden;
  position: relative;
}
.work-process-one--three .work-process-one__bg,
.work-process-one--three .work-process-one__shape1,
.work-process-one--three .work-process-one__shape2,
.work-process-one--three .work-process-one__shape3,
.work-process-one--three .work-process-one__shape4,
.work-process-one--three .work-process-one__shape5 {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.work-process-one--three .work-process-one__bg img,
.work-process-one--three .work-process-one__shape1 img,
.work-process-one--three .work-process-one__shape2 img,
.work-process-one--three .work-process-one__shape3 img,
.work-process-one--three .work-process-one__shape4 img,
.work-process-one--three .work-process-one__shape5 img {
  width: 100% !important;
  height: auto;
}
.work-process-one--three .work-process-one__bg {
  width: 100%;
  height: 80px;
  left: 0;
  bottom: 0;
  background-repeat: repeat-x;
  background-position: left top;
  animation: grassMove 40s linear 0s infinite;
}
@keyframes grassMove {
  0% {
    background-position: 1920px top;
  }
  100% {
    background-position: 0 top;
  }
}
.work-process-one--three .work-process-one__shape1 {
  top: -46px;
  left: 0;
  max-width: 290px;
  max-height: 304px;
  animation: shape1Effect 1.5s linear 0s infinite alternate;
}
@keyframes shape1Effect {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(-18px, -18px);
  }
}
@media (max-width: 640px) {
  .work-process-one--three .work-process-one__shape1 {
    display: none;
  }
}
.work-process-one--three .work-process-one__shape2 {
  top: -46px;
  right: 0;
  max-width: 212px;
  max-height: 298px;
  opacity: 0.3;
  animation: shape2Effect 1.5s linear 0s infinite alternate;
}
@keyframes shape2Effect {
  0% {
    transform: translate(0, 0);
    opacity: 0.3;
  }
  100% {
    transform: translate(18px, 18px);
    opacity: 0.6;
  }
}
@media (max-width: 640px) {
  .work-process-one--three .work-process-one__shape2 {
    display: none;
  }
}
.work-process-one--three .work-process-one__shape3 {
  left: 130px;
  bottom: 148px;
  max-width: 162px;
  max-height: 162px;
  opacity: 0.07;
  animation: shape3Effect 3.5s linear 0s infinite alternate;
  display: none;
}
@keyframes shape3Effect {
  0% {
    opacity: 0.07;
    transform: rotate(0);
  }
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
}
@media (min-width: 1200px) {
  .work-process-one--three .work-process-one__shape3 {
    display: block;
  }
}
@media (max-width: 1750px) {
  .work-process-one--three .work-process-one__shape3 {
    left: 40px;
  }
}
.work-process-one--three .work-process-one__shape4 {
  right: 9.7%;
  bottom: 30%;
  max-width: 74px;
  max-height: 74px;
  opacity: 0.65;
  animation: shape4Effect 3.5s linear 0s infinite;
  display: none;
}
@keyframes shape4Effect {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (min-width: 1200px) {
  .work-process-one--three .work-process-one__shape4 {
    display: block;
  }
}
@media (max-width: 1750px) {
  .work-process-one--three .work-process-one__shape4 {
    right: 40px;
  }
}
.work-process-one--three .work-process-one__shape5 {
  right: 13.7%;
  top: -160px;
  max-width: 290px;
  max-height: 188px;
  animation: shape5Effect 1.2s linear 0s infinite alternate;
  display: none;
}
@keyframes shape5Effect {
  0% {
    transform: translateX(-15px);
  }
  100% {
    transform: translateX(0);
  }
}
@media (min-width: 1200px) {
  .work-process-one--three .work-process-one__shape5 {
    display: block;
  }
}
.work-process-one--three .container {
  position: relative;
  z-index: 2;
}
.work-process-one--three .sec-title__title {
  color: var(--grdeen-white, #fff);
}
.work-process-one--three .work-process-one__infowrap__text {
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.5);
}
.work-process-one--three .work-process-one__col {
  background-color: transparent;
  border: 1px solid var(--grdeen-white, #fff);
}
.work-process-one--three .work-process-one__col__title {
  color: var(--grdeen-white, #fff);
}
.work-process-one--three .work-process-one__col__text {
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.5);
}

/*--------------------------------------------------------------
# Our Benefits
--------------------------------------------------------------*/
.our-benefits-one {
  position: relative;
  padding-bottom: 120px;
}
@media (max-width: 767px) {
  .our-benefits-one {
    padding-bottom: 80px;
  }
}
.our-benefits-one .container-fluid {
  padding-right: 0;
}
@media (max-width: 991px) {
  .our-benefits-one .container-fluid {
    padding-right: 15px;
  }
}
.our-benefits-one .sec-title {
  padding-bottom: 32px;
}
.our-benefits-one .sec-title__title {
  margin-top: 13px;
}
.our-benefits-one__left {
  width: 49.5%;
}
@media (max-width: 991px) {
  .our-benefits-one__left {
    width: 100%;
  }
}
.our-benefits-one__right {
  width: 50.5%;
}
@media (max-width: 991px) {
  .our-benefits-one__right {
    width: 100%;
  }
}
.our-benefits-one__content {
  position: relative;
  margin-top: -6px;
  max-width: 572px;
  margin-left: auto;
  padding-right: 20px;
}
@media (max-width: 1200px) {
  .our-benefits-one__content {
    padding-right: 0;
  }
}
@media (max-width: 991px) {
  .our-benefits-one__content {
    max-width: 100%;
    margin-bottom: 42px;
  }
}
.our-benefits-one__content__text {
  font-size: 16px;
  line-height: 30px;
  margin: 0 0 32px;
}
.our-benefits-one__content__qualitwrap {
  position: relative;
  padding: 0;
  margin-bottom: 33px;
  gap: 0 30px;
}
@media (max-width: 520px) {
  .our-benefits-one__content__qualitwrap {
    gap: 30px 0;
  }
}
.our-benefits-one__content__qualitwrap__col {
  width: calc((100% - 30px) / 2);
  background-color: var(--grdeen-white2, #f2f4ec);
  border-radius: 4px;
  padding: 16px 18px 16px 26px;
}
@media (max-width: 1200px) {
  .our-benefits-one__content__qualitwrap__col {
    padding-left: 18px;
    padding-right: 18px;
  }
}
@media (max-width: 520px) {
  .our-benefits-one__content__qualitwrap__col {
    align-items: center;
    width: 100%;
  }
}
.our-benefits-one__content__qualitwrap__col:hover .our-benefits-one__content__qualitwrap__icon {
  background-color: var(--grdeen-text-dark, #07370a);
}
.our-benefits-one__content__qualitwrap__icon {
  width: 63px;
  height: 63px;
  border-radius: 50%;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 39px;
  color: var(--grdeen-white, #fff);
  transition: all 0.5s ease;
}
.our-benefits-one__content__qualitwrap__icon--groupicon {
  font-size: 28px;
}
.our-benefits-one__content__qualitwrap__title {
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 600;
  font-size: 24px;
  line-height: 28px;
  margin-bottom: 0;
  padding-left: 16px;
  width: calc((100% - 63px) / 1);
}
.our-benefits-one__content__listwrap {
  align-items: flex-end;
}
@media (max-width: 767px) {
  .our-benefits-one__content__listwrap {
    flex-wrap: wrap;
  }
}
.our-benefits-one__content__list {
  margin: -5px 0 0 0;
  padding: 0 12px 0 0;
}
@media (max-width: 767px) {
  .our-benefits-one__content__list {
    width: 100%;
    padding: 0 0 25px 0;
  }
}
.our-benefits-one__content__list li {
  font-size: 16px;
  line-height: 24px;
  position: relative;
}
.our-benefits-one__content__list li i {
  margin-right: 6px;
}
.our-benefits-one__content__list li + li {
  margin-top: 11px;
}
.our-benefits-one__btn {
  color: var(--grdeen-white, #fff);
  background-color: var(--grdeen-text-dark, #07370a);
  border-radius: 4px;
  padding: 18px 38px;
}
.our-benefits-one__btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.our-benefits-one__image {
  position: relative;
  z-index: 2;
  padding-left: 57px;
  height: 100%;
}
@media (max-width: 991px) {
  .our-benefits-one__image {
    padding-left: 36px;
  }
}
.our-benefits-one__bigimage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}
.our-benefits-one__smimage {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  z-index: 1;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 200px 200px 0 0;
}
@media (max-width: 767px) {
  .our-benefits-one__smimage {
    padding: 160px 140px 0 0;
  }
}
.our-benefits-one__smimage img {
  width: 100%;
  max-width: 334px;
  border: 8px solid var(--grdeen-white, #fff);
  border-radius: 12px;
}
@media (max-width: 767px) {
  .our-benefits-one__smimage img {
    max-width: 250px;
  }
}
.our-benefits-one__shapetop {
  content: "";
  position: absolute;
  width: 22px;
  z-index: 2;
  background-color: var(--grdeen-base, #1a9120);
  height: 163px;
  top: 0;
  left: 24px;
  animation: aboutShapetop 1.3s linear infinite alternate;
}
@keyframes aboutShapetop {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(50%);
  }
}
@media (max-width: 991px) {
  .our-benefits-one__shapetop {
    left: 6px;
  }
}
.our-benefits-one--two {
  background-color: var(--grdeen-text-dark2, #0e150e);
  padding-top: 60px;
  padding-bottom: 55px;
  position: relative;
  overflow: hidden;
}
.our-benefits-one--two .container-fluid {
  position: relative;
  z-index: 3;
}
.our-benefits-one--two .our-benefits-one__shapetwo,
.our-benefits-one--two .our-benefits-one__shapebs1,
.our-benefits-one--two .our-benefits-one__shapebs2,
.our-benefits-one--two .our-benefits-one__shapebl1,
.our-benefits-one--two .our-benefits-one__shapebl2 {
  position: absolute;
  width: 234px;
  height: auto;
  left: 0;
  z-index: 1;
  display: none;
}
.our-benefits-one--two .our-benefits-one__shapetwo img,
.our-benefits-one--two .our-benefits-one__shapebs1 img,
.our-benefits-one--two .our-benefits-one__shapebs2 img,
.our-benefits-one--two .our-benefits-one__shapebl1 img,
.our-benefits-one--two .our-benefits-one__shapebl2 img {
  width: 100%;
}
@media (min-width: 992px) {
  .our-benefits-one--two .our-benefits-one__shapetwo,
  .our-benefits-one--two .our-benefits-one__shapebs1,
  .our-benefits-one--two .our-benefits-one__shapebs2,
  .our-benefits-one--two .our-benefits-one__shapebl1,
  .our-benefits-one--two .our-benefits-one__shapebl2 {
    display: block;
  }
}
.our-benefits-one--two .our-benefits-one__shapebl1 {
  top: -120px;
  left: 44px;
}
.our-benefits-one--two .our-benefits-one__shapebl2 {
  width: 155px;
  top: initial;
  bottom: 0;
  left: 22.5%;
}
.our-benefits-one--two .our-benefits-one__left {
  width: 50%;
  position: relative;
}
@media (max-width: 991px) {
  .our-benefits-one--two .our-benefits-one__left {
    width: 100%;
  }
}
.our-benefits-one--two .our-benefits-one__content {
  max-width: 572px;
  padding-top: 40px;
  position: relative;
  z-index: 3;
}
@media (max-width: 991px) {
  .our-benefits-one--two .our-benefits-one__content {
    max-width: 100%;
    padding-top: 0;
  }
}
.our-benefits-one--two .our-benefits-one__right {
  width: 50%;
  position: relative;
}
@media (max-width: 991px) {
  .our-benefits-one--two .our-benefits-one__right {
    width: 100%;
  }
}
.our-benefits-one--two .our-benefits-one__shapetwo {
  width: 140px;
  top: 0;
  right: 56px;
  left: initial;
  z-index: 3;
  transform: scaleX(-1);
}
.our-benefits-one--two .our-benefits-one__shapetwo__shapenormal, .our-benefits-one--two .our-benefits-one__shapetwo__shapemultiply {
  position: absolute;
  overflow: hidden;
}
.our-benefits-one--two .our-benefits-one__shapetwo__shapenormal {
  top: -60px;
  bottom: initial;
  height: 60px;
}
@media (max-width: 991px) {
  .our-benefits-one--two .our-benefits-one__shapetwo__shapenormal {
    top: -57px;
  }
}
.our-benefits-one--two .our-benefits-one__shapetwo__shapenormal img {
  margin-top: -74px;
}
.our-benefits-one--two .our-benefits-one__shapetwo__shapemultiply {
  height: 136px;
  bottom: -136px;
  top: initial;
  opacity: 0.7;
  mix-blend-mode: multiply;
}
@media (max-width: 991px) {
  .our-benefits-one--two .our-benefits-one__shapetwo__shapemultiply {
    bottom: -139px;
  }
}
.our-benefits-one--two .our-benefits-one__shapetwo__shapemultiply img {
  margin-top: -136px;
}
.our-benefits-one--two .our-benefits-one__shapebs1 {
  left: 102px;
  bottom: -37%;
}
.our-benefits-one--two .our-benefits-one__shapebs2 {
  left: initial;
  right: 170px;
  bottom: -35%;
}
.our-benefits-one--two .sec-title__title {
  color: var(--grdeen-white, #fff);
}
.our-benefits-one--two .our-benefits-one__content__qualitwrap__col {
  padding: 0;
  border-radius: 0;
  background-color: transparent;
}
.our-benefits-one--two .our-benefits-one__content__qualitwrap__col .skill-text {
  line-height: 28px;
  margin-bottom: 0;
  margin-top: 13px;
  color: #6c816c;
}
.our-benefits-one--two .our-benefits-one__content__qualitwrap__col:hover .our-benefits-one__content__qualitwrap__icon {
  background-color: transparent;
}
.our-benefits-one--two .our-benefits-one__content__qualitwrap__title {
  color: var(--grdeen-white, #fff);
  font-size: 22px;
  line-height: 26px;
}
.our-benefits-one--two .our-benefits-one__content__qualitwrap__icon {
  width: auto;
  height: auto;
  border-radius: 0;
  background-color: transparent;
  color: var(--grdeen-base, #1a9120);
  font-size: 26px;
}
.our-benefits-one--two .our-benefits-one__content__text {
  line-height: 28px;
  color: #6c816c;
}
.our-benefits-one--two .our-benefits-one__content__list li + li {
  color: #6c816c;
}
.our-benefits-one--two .our-benefits-one__content__listwrap {
  position: relative;
  padding-right: 190px;
}
@media (max-width: 767px) {
  .our-benefits-one--two .our-benefits-one__content__listwrap {
    padding-right: 0;
  }
}
.our-benefits-one--two .about-one__content__qualitwrap {
  position: relative;
}
.our-benefits-one--two .about-one__content__qualitwrap__tpright {
  position: absolute;
  width: 38px;
  right: 30px;
  top: 26px;
  z-index: 1;
  animation: skillEffect 1s linear infinite alternate;
}
@keyframes skillEffect {
  from {
    transform: rotate(8deg);
  }
  to {
    transform: rotate(-8deg);
  }
}
.our-benefits-one--two .about-one__content__qualitwrap__pricesbox {
  height: auto;
  align-items: flex-start;
  padding: 26px 46px 25px 30px;
}
@media (max-width: 520px) {
  .our-benefits-one--two .about-one__content__qualitwrap__pricesbox {
    position: relative;
  }
}
.our-benefits-one--two .about-one__content__qualitwrap__inner {
  position: relative;
  z-index: 2;
  text-align: left;
}
.our-benefits-one--two .about-one__content__qualitwrap__price {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 11px;
}
.our-benefits-one--two .about-one__content__qualitwrap__text {
  font-family: var(--grdeen-font, "Inter", sans-serif);
  font-weight: 500;
  margin-bottom: 7px;
}
.our-benefits-one--two .testimonials-card__rating__start {
  color: #ffdf09;
  font-size: 14px;
  letter-spacing: 6px;
}
.our-benefits-one--two .our-benefits-one__image {
  -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M0 0 C1.27773392 -0.00115384 2.55546784 -0.00230769 3.87192097 -0.0034965 C7.42280746 -0.00656229 10.97366527 -0.00339307 14.52454962 0.00078164 C18.38592919 0.00421803 22.24730588 0.00184462 26.10868603 0.00020415 C32.87751084 -0.00186956 39.64632686 -0.00022587 46.41515064 0.00384235 C56.48016041 0.00987486 66.54516463 0.01017839 76.61017587 0.00930037 C93.54073455 0.00798607 110.47128934 0.01132938 127.40184689 0.01731777 C144.16770224 0.02323272 160.93355596 0.0270833 177.69941235 0.02817059 C179.26248975 0.0282737 179.26248975 0.0282737 180.85714447 0.02837889 C186.14909553 0.02871722 191.4410466 0.0290186 196.73299766 0.02930576 C234.22584661 0.03140675 271.71869343 0.0381457 309.21154118 0.04760838 C345.63764783 0.05679122 382.06375391 0.0638587 418.48986149 0.06811619 C420.78004966 0.06838683 423.07023784 0.06865753 425.36042601 0.0689283 C443.968858 0.07111704 462.57729001 0.07315963 481.18572202 0.07516678 C567.25528419 0.08455587 653.32484083 0.10462584 739.3944025 0.12304783 C739.3944025 214.29304783 739.3944025 428.46304783 739.3944025 649.12304783 C616.17034 649.24679783 616.17034 649.24679783 490.4569025 649.37304783 C464.50815495 649.40946384 438.55940739 649.44587986 411.82433414 649.48339939 C380.16283512 649.5014658 380.16283512 649.5014658 365.34555912 649.50482273 C354.30454123 649.5087981 343.26360821 649.52603403 332.22262156 649.54974154 C320.95294494 649.5736119 309.68332854 649.58206823 298.41362163 649.57682763 C292.30191643 649.57442129 286.19039614 649.57819697 280.07872498 649.59990835 C274.544629 649.619448 269.01084781 649.62029333 263.47673702 649.60665226 C261.48338378 649.60496528 259.49001036 649.60995805 257.49669635 649.62257814 C224.14243435 649.82122045 197.77821449 637.39798013 174.0975275 614.18945408 C167.2485623 606.7972063 162.33532283 597.83496789 157.3944025 589.12304783 C156.40771244 587.39778634 155.42101454 585.67252927 154.43384647 583.94754124 C153.41913993 582.17317778 152.40642736 580.39768708 151.39391422 578.62207127 C148.88037155 574.21831163 146.34142674 569.82930616 143.80284977 565.43994236 C135.95669168 551.87286514 128.17832322 538.26728234 120.41051579 524.65527439 C117.40707075 519.39256863 114.40010286 514.13187597 111.39339542 508.87103367 C109.72691968 505.95512111 108.06067269 503.03907785 106.3944025 500.12304783 C105.72773693 498.95638054 105.06107027 497.78971387 104.3944025 496.62304783 C78.3944025 451.12304783 78.3944025 451.12304783 52.3944025 405.62304783 C52.06440754 405.0455579 51.73441257 404.46806797 51.39441776 403.87307835 C50.72769585 402.70631246 50.06097832 401.53954407 49.39426517 400.37277317 C47.72847966 397.45760405 46.06261823 394.54247836 44.39659977 391.62744236 C41.38599937 386.35961588 38.37596103 381.09147224 35.36873722 375.82171726 C28.56962293 363.90728638 21.75687077 352.00115979 14.8944025 340.12304783 C7.69227457 327.6570256 0.54610574 315.15943539 -6.58948421 302.65527439 C-9.59292925 297.39256863 -12.59989714 292.13187597 -15.60660458 286.87103367 C-17.27308032 283.95512111 -18.93932731 281.03907785 -20.6055975 278.12304783 C-21.27226307 276.95638054 -21.93892973 275.78971387 -22.6055975 274.62304783 C-58.6055975 211.62304783 -58.6055975 211.62304783 -59.60547543 209.87323093 C-60.27251394 208.70593389 -60.93958754 207.53865688 -61.60669613 206.37139988 C-63.26796128 203.46454044 -64.92849172 200.55726716 -66.58801937 197.64941502 C-70.007447 191.65879149 -73.43823921 185.6750506 -76.8868475 179.70117283 C-95.36518254 147.6675869 -110.3527753 117.1307498 -101.15344906 79.15380955 C-93.64815535 51.33338483 -75.67519128 27.47657158 -50.75061703 13.04931736 C-34.61790698 3.9974615 -18.45130753 -0.04079851 0 0 Z " transform="translate(103.60559749603271,-0.1230478286743164)"/></svg>');
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M0 0 C1.27773392 -0.00115384 2.55546784 -0.00230769 3.87192097 -0.0034965 C7.42280746 -0.00656229 10.97366527 -0.00339307 14.52454962 0.00078164 C18.38592919 0.00421803 22.24730588 0.00184462 26.10868603 0.00020415 C32.87751084 -0.00186956 39.64632686 -0.00022587 46.41515064 0.00384235 C56.48016041 0.00987486 66.54516463 0.01017839 76.61017587 0.00930037 C93.54073455 0.00798607 110.47128934 0.01132938 127.40184689 0.01731777 C144.16770224 0.02323272 160.93355596 0.0270833 177.69941235 0.02817059 C179.26248975 0.0282737 179.26248975 0.0282737 180.85714447 0.02837889 C186.14909553 0.02871722 191.4410466 0.0290186 196.73299766 0.02930576 C234.22584661 0.03140675 271.71869343 0.0381457 309.21154118 0.04760838 C345.63764783 0.05679122 382.06375391 0.0638587 418.48986149 0.06811619 C420.78004966 0.06838683 423.07023784 0.06865753 425.36042601 0.0689283 C443.968858 0.07111704 462.57729001 0.07315963 481.18572202 0.07516678 C567.25528419 0.08455587 653.32484083 0.10462584 739.3944025 0.12304783 C739.3944025 214.29304783 739.3944025 428.46304783 739.3944025 649.12304783 C616.17034 649.24679783 616.17034 649.24679783 490.4569025 649.37304783 C464.50815495 649.40946384 438.55940739 649.44587986 411.82433414 649.48339939 C380.16283512 649.5014658 380.16283512 649.5014658 365.34555912 649.50482273 C354.30454123 649.5087981 343.26360821 649.52603403 332.22262156 649.54974154 C320.95294494 649.5736119 309.68332854 649.58206823 298.41362163 649.57682763 C292.30191643 649.57442129 286.19039614 649.57819697 280.07872498 649.59990835 C274.544629 649.619448 269.01084781 649.62029333 263.47673702 649.60665226 C261.48338378 649.60496528 259.49001036 649.60995805 257.49669635 649.62257814 C224.14243435 649.82122045 197.77821449 637.39798013 174.0975275 614.18945408 C167.2485623 606.7972063 162.33532283 597.83496789 157.3944025 589.12304783 C156.40771244 587.39778634 155.42101454 585.67252927 154.43384647 583.94754124 C153.41913993 582.17317778 152.40642736 580.39768708 151.39391422 578.62207127 C148.88037155 574.21831163 146.34142674 569.82930616 143.80284977 565.43994236 C135.95669168 551.87286514 128.17832322 538.26728234 120.41051579 524.65527439 C117.40707075 519.39256863 114.40010286 514.13187597 111.39339542 508.87103367 C109.72691968 505.95512111 108.06067269 503.03907785 106.3944025 500.12304783 C105.72773693 498.95638054 105.06107027 497.78971387 104.3944025 496.62304783 C78.3944025 451.12304783 78.3944025 451.12304783 52.3944025 405.62304783 C52.06440754 405.0455579 51.73441257 404.46806797 51.39441776 403.87307835 C50.72769585 402.70631246 50.06097832 401.53954407 49.39426517 400.37277317 C47.72847966 397.45760405 46.06261823 394.54247836 44.39659977 391.62744236 C41.38599937 386.35961588 38.37596103 381.09147224 35.36873722 375.82171726 C28.56962293 363.90728638 21.75687077 352.00115979 14.8944025 340.12304783 C7.69227457 327.6570256 0.54610574 315.15943539 -6.58948421 302.65527439 C-9.59292925 297.39256863 -12.59989714 292.13187597 -15.60660458 286.87103367 C-17.27308032 283.95512111 -18.93932731 281.03907785 -20.6055975 278.12304783 C-21.27226307 276.95638054 -21.93892973 275.78971387 -22.6055975 274.62304783 C-58.6055975 211.62304783 -58.6055975 211.62304783 -59.60547543 209.87323093 C-60.27251394 208.70593389 -60.93958754 207.53865688 -61.60669613 206.37139988 C-63.26796128 203.46454044 -64.92849172 200.55726716 -66.58801937 197.64941502 C-70.007447 191.65879149 -73.43823921 185.6750506 -76.8868475 179.70117283 C-95.36518254 147.6675869 -110.3527753 117.1307498 -101.15344906 79.15380955 C-93.64815535 51.33338483 -75.67519128 27.47657158 -50.75061703 13.04931736 C-34.61790698 3.9974615 -18.45130753 -0.04079851 0 0 Z " transform="translate(103.60559749603271,-0.1230478286743164)"/></svg>');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: right center;
  mask-position: right center;
  -webkit-mask-size: cover;
  mask-size: cover;
  padding-left: 0;
  margin-left: auto;
  max-width: 843px;
}
.our-benefits-one--three {
  background-color: transparent;
  position: relative;
  padding-top: 120px;
  padding-bottom: 0;
}
@media (max-width: 767px) {
  .our-benefits-one--three {
    padding-top: 80px;
  }
}
@media (max-width: 1200px) {
  .our-benefits-one--three .container {
    max-width: 100%;
  }
}
.our-benefits-one--three .our-benefits-one__bg {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  max-height: 85px;
  z-index: 1;
  background-repeat: repeat-x;
  background-position: center top;
  animation: skillEffectGrass 40s linear 0s infinite;
}
@keyframes skillEffectGrass {
  0% {
    background-position: -1920px top;
  }
  100% {
    background-position: 0 top;
  }
}
.our-benefits-one--three .our-benefits-one__image {
  padding-left: 0;
  padding-right: 12px;
  mask-image: initial;
  position: relative;
  z-index: 2;
  max-height: 670px;
}
@media (max-width: 991px) {
  .our-benefits-one--three .our-benefits-one__image {
    padding-right: 0;
    text-align: center;
  }
}
.our-benefits-one--three .our-benefits-one__image__shape1, .our-benefits-one--three .our-benefits-one__image__shape2 {
  position: absolute;
  top: 112px;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
}
.our-benefits-one--three .our-benefits-one__image__shape1 {
  max-width: 270px;
  z-index: 2;
  right: 10px;
  top: 184px;
  animation: skillEffec3_1 1.2s linear 0s infinite alternate;
}
@keyframes skillEffec3_1 {
  0% {
    transform: translateX(-8px);
  }
  100% {
    transform: translateX(0);
  }
}
@media (max-width: 767px) {
  .our-benefits-one--three .our-benefits-one__image__shape1 {
    left: 0;
    right: 0;
    top: 20px;
    max-width: 300px;
    margin: 0 auto;
  }
}
.our-benefits-one--three .our-benefits-one__image__shape2 {
  max-width: 574px;
  z-index: 1;
  left: -60px;
  animation: skillEffec3_2 1.6s linear 0s infinite alternate;
}
@keyframes skillEffec3_2 {
  0% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}
@media (max-width: 767px) {
  .our-benefits-one--three .our-benefits-one__image__shape2 {
    display: none;
  }
}
.our-benefits-one--three .our-benefits-one__bigimage {
  position: relative;
  z-index: 4;
  object-position: center top;
  max-width: 528px;
}
.our-benefits-one--three .our-benefits-one__content {
  padding: 0 0 75px 35px;
  max-width: initial;
}
@media (max-width: 1199px) {
  .our-benefits-one--three .our-benefits-one__content {
    padding-left: 0;
  }
}
@media (max-width: 991px) {
  .our-benefits-one--three .our-benefits-one__content {
    padding-top: 45px;
  }
}
.our-benefits-one--three .our-benefits-one__content__qualitwrap__title {
  color: var(--grdeen-text-dark, #07370a);
}
@media (max-width: 520px) {
  .our-benefits-one--three .our-benefits-one__content__listwrap {
    padding-right: 0;
  }
}
.our-benefits-one--three .sec-title__title {
  color: var(--grdeen-text-dark, #07370a);
}

/*--------------------------------------------------------------
# Our project
--------------------------------------------------------------*/
.project-one {
  padding: 112px 0;
  padding-bottom: 120px;
  position: relative;
  background-color: #eff6f0;
}
@media (max-width: 991px) {
  .project-one {
    padding: 80px 0;
  }
}
.project-one__bg {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
  opacity: 0.5;
  mix-blend-mode: overlay;
}
.project-one .container {
  position: relative;
  z-index: 2;
  max-width: 1350px;
}
@media (max-width: 1200px) {
  .project-one .container {
    max-width: 100%;
  }
}
.project-one .sec-title {
  padding-bottom: 50px;
}
.project-one .sec-title__title {
  margin-top: 1px;
}
.project-one__item {
  position: relative;
}
.project-one__item__image {
  width: 100%;
  overflow: hidden;
  border-radius: 4px;
  position: relative;
}
.project-one__item__image::after {
  content: "";
  position: absolute;
  width: calc(100% + 200px);
  height: calc(100% + 204px);
  left: -22%;
  right: 0;
  bottom: -150%;
  z-index: 1;
  transform: rotate(133.6deg);
  margin: 0 auto;
  background-color: rgba(var(--grdeen-text-dark-rgb, 7, 55, 10), 0.4);
  opacity: 0;
  visibility: hidden;
  transition: all 0.6s ease;
}
.project-one__item__image > img {
  width: 100% !important;
  transition: all 0.5s ease;
  border-radius: 4px !important;
}
@media (max-width: 991px) {
  .project-one__item__image > img {
    max-height: 450px;
    min-height: 450px;
    height: 100%;
    object-fit: cover;
    object-position: center top;
  }
}
@media (max-width: 767px) {
  .project-one__item__image > img {
    max-height: initial;
    min-height: initial;
  }
}
.project-one__item__info {
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 32px;
  z-index: 1;
  padding: 0 22px;
}
.project-one__item__bg {
  padding: 23px 76px 18px 33px;
  background-color: var(--grdeen-white3, #faf8ec);
  position: absolute;
  left: 0;
  bottom: 0;
  width: calc(100% - 50px);
  transition: all 0.5s ease;
  border-radius: 0 42px 42px 0;
}
@media (max-width: 991px) {
  .project-one__item__bg {
    width: calc(100% - 20px);
  }
}
.project-one__item__tagtext {
  color: var(--grdeen-base, #1a9120);
  font-weight: 500;
  font-size: 13px;
  line-height: 1;
  text-transform: uppercase;
  display: block;
  margin-bottom: 4px;
}
.project-one__item__heading {
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 600;
  font-size: 22px;
  line-height: 26px;
  margin-bottom: 0;
}
.project-one__item__heading > a {
  color: inherit;
  background: linear-gradient(to right, currentcolor 0%, currentcolor 100%) 0px 95%/0px 1px no-repeat;
  transition: all 500ms ease;
}
.project-one__item__heading > a:hover {
  background-size: 100% 1px;
}
.project-one__item__heading:hover {
  color: var(--grdeen-base, #1a9120);
}
.project-one__item__tex-info {
  color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.8);
  font-size: 14px;
  line-height: 28px;
  margin-bottom: 0;
  padding-top: 22px;
  transition: all 0.2s ease;
}
.project-one__item__right-arrow {
  position: absolute;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  right: 10px;
  bottom: 0;
  top: 0;
  margin: auto 0;
  background-color: var(--grdeen-base, #1a9120);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s ease;
  color: var(--grdeen-white, #fff);
  font-size: 17px;
}
.project-one__item__right-arrow svg {
  width: 1em;
  height: 1em;
  fill: currentColor;
}
@media (max-width: 1400px) {
  .project-one__item__right-arrow {
    right: 20px;
  }
}
.project-one__item:hover .project-one__item__image::after {
  opacity: 1;
  visibility: visible;
  bottom: -25.5%;
}
.project-one__item:hover .project-one__item__image > img {
  transform: scale(1.08);
}
.project-one__item:hover .project-one__item__right-arrow {
  background-color: var(--grdeen-text-dark, #07370a);
  transform: translateY(-5px);
}
.project-one__moreproject {
  border-radius: 3px;
  max-width: 666px;
  margin: 0 auto;
  margin-top: 56px;
  padding-left: 27px;
  border: 1px solid var(--grdeen-base, #1a9120);
}
@media (max-width: 767px) {
  .project-one__moreproject {
    border: 0;
  }
}
.project-one__moreproject__title {
  color: var(--grdeen-text-dark, #07370a);
  font-weight: 500;
  font-size: 20px;
  line-height: 25px;
  margin-bottom: 0;
  padding-right: 12px;
}
@media (max-width: 767px) {
  .project-one__moreproject__title {
    display: none;
  }
}
.project-one__moreproject__btn {
  padding: 20.25px 22px;
  border-radius: 3px;
}
@media (max-width: 767px) {
  .project-one__moreproject__btn {
    margin: 0 auto;
  }
}
.project-one--two {
  margin-bottom: -28px;
  background-color: #e9f3ea;
  padding-bottom: 0;
}
.project-one--two .container-fluid {
  position: relative;
  z-index: 2;
  padding: 0;
}
.project-one--two .project-one__sctwrap {
  max-width: 1170px;
  margin: 0 auto;
  padding-bottom: 55px;
}
@media (max-width: 1199px) {
  .project-one--two .project-one__sctwrap {
    padding-left: 20px;
    padding-right: 20px;
  }
  .project-one--two .project-one__sctwrap .sec-title__title {
    font-size: 43px;
  }
}
@media (max-width: 991px) {
  .project-one--two .project-one__sctwrap {
    flex-wrap: wrap;
  }
}
.project-one--two .project-one__sctwrap .project-one__moreproject__btn {
  padding: 15.5px 31.5px;
  background-color: var(--grdeen-text-dark, #07370a);
}
.project-one--two .project-one__sctwrap .project-one__moreproject__btn::before {
  background-color: var(--grdeen-base, #1a9120);
}
.project-one--two .sec-title {
  width: 50%;
  padding-bottom: 0;
}
@media (max-width: 991px) {
  .project-one--two .sec-title {
    width: 100%;
  }
}
.project-one--two .project-one__info {
  width: 50%;
  padding-left: 160px;
}
@media (max-width: 991px) {
  .project-one--two .project-one__info {
    width: 100%;
    padding-left: 0;
    padding-top: 35px;
  }
}
.project-one--two .project-one__info__text {
  font-size: 16px;
  line-height: 28px;
  color: var(--grdeen-text-dark, #07370a);
  margin-bottom: 16px;
}
.project-one--two .project-one__item__image::after {
  display: none;
}
.project-one--two .project-one__item__image img {
  width: 100% !important;
}
.project-one--two .project-one__item__bg {
  width: calc(100% - 0px);
  max-width: 370px;
  margin: 0 auto;
  right: 0;
  background-color: var(--grdeen-white, #fff);
  border-radius: 48px;
  padding: 45px 50px 18px 50px;
  text-align: center;
}
.project-one--two .project-one__item__right-arrow {
  left: 0;
  right: 0;
  margin: 0 auto;
  top: -24px;
  bottom: initial;
}
.project-one--two .owl-carousel .owl-nav.disabled + .owl-dots {
  margin-top: 52px;
}
.project-one--two .grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot span {
  background-color: #8ece91;
}
.project-one--two .grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot:hover span,
.project-one--two .grdeen-owl__carousel--basic-nav.owl-carousel .owl-dots .owl-dot.active span {
  background-color: var(--grdeen-white, #fff);
}
.project-one--two .owl-carousel .owl-nav.disabled {
  display: none;
}
.project-one--two .project-one__moreproject {
  max-width: 970px;
  border: 0;
  background-color: var(--grdeen-base, #1a9120);
  padding-left: 16px;
  margin-top: 34px;
  position: relative;
  top: 27px;
}
@media (max-width: 991px) {
  .project-one--two .project-one__moreproject {
    padding: 20px;
  }
}
@media (max-width: 767px) {
  .project-one--two .project-one__moreproject {
    flex-direction: column;
  }
}
.project-one--two .project-one__moreproject__title {
  display: flex;
  align-items: center;
  color: var(--grdeen-white, #fff);
}
.project-one--two .project-one__moreproject__title span {
  display: inline-block;
  padding-left: 12px;
}
.project-one--two .project-one__moreproject img {
  width: 46px;
  filter: brightness(0) invert(1);
}
.project-one--two .project-one__moreproject .project-one__moreproject__btn {
  background-color: var(--grdeen-text-dark, #07370a);
  padding-left: 33px;
  padding-right: 33px;
}
@media (max-width: 767px) {
  .project-one--two .project-one__moreproject .project-one__moreproject__btn {
    margin: 20px 0 0;
  }
}
.project-one--three {
  position: relative;
  z-index: 2;
  margin: 0;
  padding: 0;
  background-color: transparent;
}
.project-one--three .owl-carousel .owl-dots .owl-dot span {
  background-color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 0.5);
}
.project-one--three .owl-carousel .owl-dots .owl-dot.active span {
  background-color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 1);
  border-color: rgba(var(--grdeen-white-rgb, 255, 255, 255), 1);
}
.project-one--page {
  background-color: transparent;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .project-one--page {
    padding: 80px 0;
  }
}

/*--------------------------------------------------------------
# Project details
--------------------------------------------------------------*/
.project-details {
  position: relative;
  padding: 120px 0;
}
@media (max-width: 767px) {
  .project-details {
    padding: 80px 0;
  }
}
.project-details__image {
  position: relative;
  margin: 0 0 40px;
}
.project-details__image img {
  width: 100%;
  height: auto;
  border-radius: 5px;
}
.project-details__content {
  position: relative;
}
.project-details__content__image {
  position: relative;
}
.project-details__content__image img {
  width: 100%;
  height: auto;
  border-radius: 5px;
}
.project-details__title {
  font-size: 42px;
  font-weight: 700;
  color: var(--grdeen-black, #172000);
  margin: 0 0 20px;
}
@media (max-width: 767px) {
  .project-details__title {
    font-size: 32px;
  }
}
.project-details__text {
  line-height: 30px;
  margin: 0 0 16px;
}
.project-details__text:last-child {
  margin-bottom: 0;
}
.project-details__heading {
  font-size: 24px;
  font-weight: 700;
  color: var(--grdeen-black, #172000);
  margin: 31px 0 20px;
}
.project-details__benefit {
  position: relative;
  margin-top: -10px;
  margin-bottom: 40px;
}
.project-details__benefit img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}
.project-details__benefit__content {
  position: relative;
}
@media (min-width: 1200px) {
  .project-details__benefit__content {
    margin-left: -47px;
  }
}
.project-details__benefit__title {
  margin: -5px 0 18px;
  font-size: 24px;
  font-weight: 600;
  color: var(--grdeen-black, #172000);
}
.project-details__benefit__text {
  margin: 0;
  font-size: 15px;
  line-height: 28px;
}
.project-details__benefit__list {
  margin: 25px 0 0;
  padding: 0;
  list-style: none;
}
.project-details__benefit__list li {
  position: relative;
  font-size: 16px;
  line-height: 32px;
  font-weight: 500;
  padding-left: 29px;
  color: var(--grdeen-black, #172000);
  margin-bottom: 0;
}
.project-details__benefit__list__icon {
  position: absolute;
  top: 1px;
  left: 0;
  font-size: 17px;
  color: #000;
  display: inline-block;
}
.project-details__heading-two {
  font-size: 24px;
  line-height: 38px;
  font-weight: 600;
  color: var(--grdeen-base, #1a9120);
  margin: 31px 0 12px;
}
.project-details__sidebar {
  position: relative;
}
@media (min-width: 1200px) {
  .project-details__sidebar {
    padding-left: 60px;
  }
}
.project-details__info {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  background-color: #0d1b0c;
  background-blend-mode: overlay;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 36px 40px 41px;
}
.project-details__info__list {
  margin: 0;
  padding: 0;
  list-style: none;
}
.project-details__info__list li {
  position: relative;
}
.project-details__info__list li + li {
  border-top: 1px solid var(--grdeen-base, #1a9120);
  padding-top: 9px;
  margin-top: 11px;
}
.project-details__info__list__title {
  display: block;
  font-size: 16px;
  color: rgb(var(--grdeen-white-rgb, 255, 255, 255), 0.6);
  margin: 0 0 5px;
}
.project-details__info__list__text {
  font-size: 18px;
  color: var(--grdeen-white, #fff);
  font-weight: 600;
  margin: 0;
}
.project-details__info-two {
  position: relative;
  overflow: hidden;
  border-radius: 5px;
  background-color: #053507;
  background-blend-mode: overlay;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 121px 30px 59px;
  text-align: center;
  margin-top: 20px;
}
.project-details__info-two__title {
  display: block;
  font-size: 22px;
  font-weight: 400;
  line-height: 30px;
  color: var(--grdeen-white, #fff);
  margin: 0 0 27px;
}
.project-details__info-two .grdeen-btn {
  padding: 11.3px 33px;
}

.related-project {
  position: relative;
  padding: 0 0 120px;
}
@media (max-width: 767px) {
  .related-project {
    padding: 0 0 80px;
  }
}
.related-project .container {
  position: relative;
  z-index: 2;
  max-width: 1350px;
}
.related-project__title {
  text-align: center;
  font-size: 42px;
  font-weight: 700;
  line-height: 48px;
  color: var(--grdeen-black, #172000);
  margin: 0 0 27px;
}
@media (max-width: 767px) {
  .related-project__title {
    font-size: 32px;
  }
}

/*--------------------------------------------------------------
# Free Booking
--------------------------------------------------------------*/
.free-booking-one {
  overflow: hidden;
  position: relative;
  background-color: #0f3c12;
}
.free-booking-one__bg {
  position: absolute;
  left: 0;
  left: 0;
  width: 76%;
  height: 100%;
  background-position: left top;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: overlay;
}
@media (max-width: 1199px) {
  .free-booking-one__bg {
    width: 100%;
  }
}
.free-booking-one .container {
  position: relative;
  z-index: 2;
}
.free-booking-one__content {
  position: relative;
  z-index: 2;
  padding: 109px 0 114px;
}
@media (max-width: 991px) {
  .free-booking-one__content {
    padding: 80px 0;
  }
}
.free-booking-one__form {
  position: relative;
}
@media (min-width: 1200px) {
  .free-booking-one__form {
    padding-right: 50px;
  }
}
.free-booking-one .sec-title {
  padding-bottom: 38px;
}
.free-booking-one .sec-title__img, .free-booking-one .sec-title__tagline, .free-booking-one .sec-title__title {
  color: var(--grdeen-white, #fff);
}
.free-booking-one .dropdown-toggle::after {
  border: 0;
  font-family: "icomoon" !important;
  content: "\e924";
  margin: 0;
  margin-right: -8px;
  font-size: 16px;
}
.free-booking-one .bootstrap-select .dropdown-menu {
  top: -4px !important;
}
.free-booking-one__submit {
  background-color: var(--grdeen-text-dark, #07370a);
  border-radius: 4px;
  padding: 18px 32px;
}
.free-booking-one__submit::before {
  background-color: var(--grdeen-white, #fff);
}
.free-booking-one__submit:hover {
  color: var(--grdeen-text-dark, #07370a);
}
.free-booking-one__image {
  position: relative;
  padding-top: 6px;
}
.free-booking-one__image__maskingimg {
  max-width: 588px;
  width: 100%;
  height: 100%;
  -webkit-mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M0 0 C1.39567098 -0.00409829 1.39567098 -0.00409829 2.81953734 -0.00827938 C3.85442284 -0.00615427 4.88930834 -0.00402915 5.95555401 -0.00183964 C7.04672569 -0.00346731 8.13789736 -0.00509498 9.26213485 -0.00677198 C12.95395741 -0.01085281 16.64573511 -0.00752411 20.33755779 -0.00405216 C22.30271865 -0.00470904 24.26787954 -0.00537177 26.23304017 -0.00651281 C33.37390423 -0.01064134 40.51474837 -0.00732202 47.65560974 -0.00060903 C52.98707528 0.00440238 58.31853885 0.00746377 63.65000629 0.00953007 C130.43909939 0.03818569 197.22495287 0.44607019 264.01196767 0.9434516 C275.86461314 1.03166925 287.71728262 1.11564976 299.56997967 1.19663143 C313.72974281 1.29338122 327.889489 1.39217423 342.04921371 1.49440867 C343.56884436 1.50537975 345.08847501 1.5163508 346.60810566 1.52732182 C347.35382924 1.53270932 348.09955282 1.53809683 348.86787408 1.5436476 C353.95162567 1.58026677 359.035385 1.61562297 364.11914921 1.65044117 C370.30816893 1.69289072 376.49716597 1.73781264 382.68614837 1.78540128 C385.79838634 1.80928895 388.91062324 1.83206885 392.02288532 1.85260677 C418.59389762 2.0294673 445.14698929 2.62523841 471.71719646 3.12656307 C471.71719646 266.46656307 471.71719646 529.80656307 471.71719646 801.12656307 C348.77157146 801.59062557 348.77157146 801.59062557 223.34219646 802.06406307 C197.43413494 802.20062313 171.52607342 802.33718319 144.83291912 802.47788143 C113.26675701 802.54563046 113.26675701 802.54563046 98.50293255 802.55821896 C87.47962651 802.57315308 76.45751004 802.63788247 65.43464245 802.72666448 C54.19426676 802.81597502 42.95473976 802.84793382 31.71393832 802.82823733 C25.61239377 802.81918616 19.51345131 802.83368846 13.41238236 802.91479003 C7.89521242 802.98768514 2.38248761 802.99150436 -3.13488483 802.94007969 C-5.12954738 802.93371456 -7.1244864 802.95278511 -9.1186049 802.99980175 C-23.64808014 803.32225377 -23.64808014 803.32225377 -28.0432463 799.65946873 C-31.1541252 796.11516888 -33.36279878 792.19823614 -35.38902862 787.95945806 C-36.66062894 785.35174351 -38.20134332 782.93137847 -39.75155354 780.48203182 C-40.7672122 778.76246098 -41.77763616 777.03978754 -42.78280354 775.31406307 C-43.33266926 774.37522274 -43.88253498 773.43638241 -44.4490633 772.46909237 C-49.53018073 763.7610295 -54.50749447 755.00618556 -59.28280354 746.12656307 C-59.79391682 745.17813534 -60.3050301 744.2297076 -60.83163166 743.25253963 C-96.66634685 676.38504571 -120.76465185 603.94413656 -137.53280354 498.56406307 C-137.68394611 497.29546444 -137.83508869 496.02686581 -137.99081135 494.71982479 C-138.37311693 491.32006669 -138.72773674 487.92068372 -139.05395222 484.51523495 C-139.21390542 482.84569805 -139.3786638 481.17661766 -139.54705524 479.50791073 C-141.44152893 460.33024509 -141.62626783 441.21330344 -141.59847736 421.96298885 C-141.59386695 418.50647551 -141.6007087 415.05031587 -141.61898518 411.59384823 C-141.77728132 381.32921655 -140.25735389 351.36452263 -135.97030354 321.37656307 C-135.86642323 320.6307095 -135.76254292 319.88485592 -135.65551472 319.11640072 C-125.54486432 246.70917774 -103.94769111 176.97861029 -72.28280354 111.12656307 C-71.60121174 109.70561337 -71.60121174 109.70561337 -70.90585041 108.2559576 C-63.96235905 93.86694547 -56.30205724 79.94082474 -48.28280354 66.12656307 C-47.82389729 65.32669979 -47.36499104 64.52683651 -46.89217854 63.70273495 C-40.09201573 51.87245171 -32.64929007 40.48390369 -25.09530354 29.12656307 C-24.39378666 28.06952175 -24.39378666 28.06952175 -23.67809772 26.99112606 C-5.73592409 0.00209795 -5.73592409 0.00209795 0 0 Z " transform="translate(141.28280353546143,-0.12656307220458984)"/></svg>');
  mask: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M0 0 C1.39567098 -0.00409829 1.39567098 -0.00409829 2.81953734 -0.00827938 C3.85442284 -0.00615427 4.88930834 -0.00402915 5.95555401 -0.00183964 C7.04672569 -0.00346731 8.13789736 -0.00509498 9.26213485 -0.00677198 C12.95395741 -0.01085281 16.64573511 -0.00752411 20.33755779 -0.00405216 C22.30271865 -0.00470904 24.26787954 -0.00537177 26.23304017 -0.00651281 C33.37390423 -0.01064134 40.51474837 -0.00732202 47.65560974 -0.00060903 C52.98707528 0.00440238 58.31853885 0.00746377 63.65000629 0.00953007 C130.43909939 0.03818569 197.22495287 0.44607019 264.01196767 0.9434516 C275.86461314 1.03166925 287.71728262 1.11564976 299.56997967 1.19663143 C313.72974281 1.29338122 327.889489 1.39217423 342.04921371 1.49440867 C343.56884436 1.50537975 345.08847501 1.5163508 346.60810566 1.52732182 C347.35382924 1.53270932 348.09955282 1.53809683 348.86787408 1.5436476 C353.95162567 1.58026677 359.035385 1.61562297 364.11914921 1.65044117 C370.30816893 1.69289072 376.49716597 1.73781264 382.68614837 1.78540128 C385.79838634 1.80928895 388.91062324 1.83206885 392.02288532 1.85260677 C418.59389762 2.0294673 445.14698929 2.62523841 471.71719646 3.12656307 C471.71719646 266.46656307 471.71719646 529.80656307 471.71719646 801.12656307 C348.77157146 801.59062557 348.77157146 801.59062557 223.34219646 802.06406307 C197.43413494 802.20062313 171.52607342 802.33718319 144.83291912 802.47788143 C113.26675701 802.54563046 113.26675701 802.54563046 98.50293255 802.55821896 C87.47962651 802.57315308 76.45751004 802.63788247 65.43464245 802.72666448 C54.19426676 802.81597502 42.95473976 802.84793382 31.71393832 802.82823733 C25.61239377 802.81918616 19.51345131 802.83368846 13.41238236 802.91479003 C7.89521242 802.98768514 2.38248761 802.99150436 -3.13488483 802.94007969 C-5.12954738 802.93371456 -7.1244864 802.95278511 -9.1186049 802.99980175 C-23.64808014 803.32225377 -23.64808014 803.32225377 -28.0432463 799.65946873 C-31.1541252 796.11516888 -33.36279878 792.19823614 -35.38902862 787.95945806 C-36.66062894 785.35174351 -38.20134332 782.93137847 -39.75155354 780.48203182 C-40.7672122 778.76246098 -41.77763616 777.03978754 -42.78280354 775.31406307 C-43.33266926 774.37522274 -43.88253498 773.43638241 -44.4490633 772.46909237 C-49.53018073 763.7610295 -54.50749447 755.00618556 -59.28280354 746.12656307 C-59.79391682 745.17813534 -60.3050301 744.2297076 -60.83163166 743.25253963 C-96.66634685 676.38504571 -120.76465185 603.94413656 -137.53280354 498.56406307 C-137.68394611 497.29546444 -137.83508869 496.02686581 -137.99081135 494.71982479 C-138.37311693 491.32006669 -138.72773674 487.92068372 -139.05395222 484.51523495 C-139.21390542 482.84569805 -139.3786638 481.17661766 -139.54705524 479.50791073 C-141.44152893 460.33024509 -141.62626783 441.21330344 -141.59847736 421.96298885 C-141.59386695 418.50647551 -141.6007087 415.05031587 -141.61898518 411.59384823 C-141.77728132 381.32921655 -140.25735389 351.36452263 -135.97030354 321.37656307 C-135.86642323 320.6307095 -135.76254292 319.88485592 -135.65551472 319.11640072 C-125.54486432 246.70917774 -103.94769111 176.97861029 -72.28280354 111.12656307 C-71.60121174 109.70561337 -71.60121174 109.70561337 -70.90585041 108.2559576 C-63.96235905 93.86694547 -56.30205724 79.94082474 -48.28280354 66.12656307 C-47.82389729 65.32669979 -47.36499104 64.52683651 -46.89217854 63.70273495 C-40.09201573 51.87245171 -32.64929007 40.48390369 -25.09530354 29.12656307 C-24.39378666 28.06952175 -24.39378666 28.06952175 -23.67809772 26.99112606 C-5.73592409 0.00209795 -5.73592409 0.00209795 0 0 Z " transform="translate(141.28280353546143,-0.12656307220458984)"/></svg>');
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-position: left center;
  mask-position: left center;
  -webkit-mask-size: cover;
  mask-size: cover;
  margin-left: auto;
}
.free-booking-one__image__maskingimg img {
  width: 100%;
}
@media (max-width: 991px) {
  .free-booking-one__image__maskingimg img {
    width: 100%;
  }
}
.free-booking-one__image__shape {
  position: absolute;
  left: 0;
  top: 0;
}
@media (min-width: 1400px) {
  .free-booking-one__image__shape {
    left: 66px;
  }
}
@media (max-width: 767px) {
  .free-booking-one__image__shape {
    display: none;
  }
}
.free-booking-one__image__shape img {
  max-width: 100%;
}
.free-booking-one__shadow {
  position: absolute;
  left: 17px;
  bottom: -92px;
  z-index: 1;
  mix-blend-mode: overlay;
}
.free-booking-one__shadow__title {
  color: #859286;
  font-weight: 600;
  font-size: 200px;
  line-height: 1;
}
.free-booking-one--three {
  background-color: #09290b;
  position: relative;
}
.free-booking-one--three .free-booking-one__bg {
  height: 100%;
  width: 82.6%;
  position: absolute;
  left: 0;
  top: 0;
  background-position: right top;
  background-repeat: no-repeat;
  background-size: cover;
  mix-blend-mode: normal;
  z-index: 1;
}
@media (max-width: 1199px) {
  .free-booking-one--three .free-booking-one__bg {
    display: none;
  }
}
.free-booking-one--three .container {
  z-index: inherit;
}
.free-booking-one--three .col-xl-6:first-child {
  position: relative;
  z-index: 2;
}
.free-booking-one--three .free-booking-one__shape1 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 42px;
  max-width: 216px;
  max-height: 303px;
  opacity: 0.3;
  animation: bookingEffect1 1.5s linear 0s infinite alternate-reverse;
  z-index: 2;
}
@media (max-width: 1199px) {
  .free-booking-one--three .free-booking-one__shape1 {
    display: none;
  }
}
@keyframes bookingEffect1 {
  0% {
    transform: translateY(-5px) skewY(-5deg);
  }
  100% {
    transform: translateY(5px) skewY(5deg);
  }
}
.free-booking-one--three .free-booking-one__shape2 {
  position: absolute;
  width: 100%;
  height: auto;
  top: 20%;
  left: 56%;
  max-width: 325px;
  animation: bookingEffect1 1.5s linear 0s infinite alternate-reverse;
  z-index: 2;
}
@media (max-width: 1499px) {
  .free-booking-one--three .free-booking-one__shape2 {
    left: 46%;
  }
}
@media (max-width: 1299px) {
  .free-booking-one--three .free-booking-one__shape2 {
    left: 41%;
  }
}
@media (max-width: 1199px) {
  .free-booking-one--three .free-booking-one__shape2 {
    display: none;
  }
}
.free-booking-one--three .free-booking-one__shape3 {
  z-index: 2;
  left: 40px;
  bottom: 90px;
  position: absolute;
  animation: skillEffec3_1 1.2s linear 0s infinite alternate;
}
@media (max-width: 1499px) {
  .free-booking-one--three .free-booking-one__shape3 {
    display: none;
  }
}
.free-booking-one--three .free-booking-one__left {
  width: 52%;
  position: relative;
  z-index: 2;
}
@media (max-width: 991px) {
  .free-booking-one--three .free-booking-one__left {
    width: 100%;
  }
}
.free-booking-one--three .free-booking-one__right {
  width: 48%;
}
@media (max-width: 991px) {
  .free-booking-one--three .free-booking-one__right {
    width: 100%;
  }
}
.free-booking-one--three .free-booking-one__image {
  padding: 0;
}
@media (min-width: 1200px) {
  .free-booking-one--three .free-booking-one__image {
    margin-left: -288px;
  }
}
@media (min-width: 1499px) {
  .free-booking-one--three .free-booking-one__image {
    margin-left: -138px;
  }
}
.free-booking-one--three .free-booking-one__image__maskingimg {
  max-width: 1120px;
  mask: inherit;
  position: relative;
  z-index: -1;
}
.free-booking-one--three .free-booking-one__image__maskingimg img {
  width: auto;
  border-radius: 0;
}
@media (max-width: 991px) {
  .free-booking-one--three .free-booking-one__image__maskingimg img {
    width: 100%;
  }
}
.free-booking-one--three .free-booking-one__submit {
  background-color: var(--grdeen-base, #1a9120);
}

/*--------------------------------------------------------------
# companies
--------------------------------------------------------------*/
.companies-one {
  padding: 120px 0;
  padding-top: 0;
}
@media (max-width: 767px) {
  .companies-one {
    padding: 80px 0;
    padding-top: 0;
  }
}
@media (max-width: 1200px) {
  .companies-one .container {
    max-width: 100%;
  }
}
.companies-one__sctwrap .sec-title {
  text-align: center;
  padding-bottom: 65px;
}
@media (max-width: 991px) {
  .companies-one__sctwrap .sec-title {
    padding-bottom: 40px;
  }
}
.companies-one__sctwrap .sec-title__title {
  margin-top: 0;
  font-size: 45px;
  line-height: 50px;
}
@media (max-width: 767px) {
  .companies-one__sctwrap .sec-title__title {
    font-size: 32px;
    line-height: 34px;
  }
}
.companies-one__carousel .owl-stage-outer {
  overflow: hidden;
}
.companies-one__carousel.owl-carousel .owl-nav, .companies-one__carousel.owl-carousel .owl-dots {
  display: none !important;
}
.companies-one__image {
  overflow: initial;
}
.companies-one__inner-img {
  min-height: 82px;
  max-height: 82px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 26px;
  position: relative;
}
.companies-one__inner-img::after {
  content: "";
  position: absolute;
  width: auto;
  height: 84px;
  left: -1px;
  top: 20px;
  bottom: 0;
  margin: auto 0;
  z-index: 1;
  border-left: 1px solid #e4e4e4;
}
.companies-one__inner-img img {
  width: 100% !important;

  transition: all 0.5s ease;
}
.companies-one__inner-img:hover img {
  filter: opacity(1) brightness(1);
}
/*# sourceMappingURL=grdeen.css.map */
