<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Helmet- Construction Management System' ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <?= $this->renderSection('head') ?>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar-collapsed {
            width: 4rem;
        }
        .sidebar-expanded {
            width: 16rem;
        }
        .sidebar-text {
            opacity: 1;
            transition: opacity 0.3s ease;
        }
        .sidebar-collapsed .sidebar-text {
            opacity: 0;
            pointer-events: none;
        }
        .sidebar-transition {
            transition: width 0.3s ease;
        }
        .sidebar-mobile {
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }
        .sidebar-mobile.open {
            transform: translateX(0);
        }
        @media (min-width: 768px) {
            .sidebar-mobile {
                transform: translateX(0);
            }
        }
        .tooltip {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.2s, visibility 0.2s;
        }
        .submenu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        .submenu.open {
            max-height: 200px;
        }
        .menu-chevron {
            transition: transform 0.3s ease;
        }
        .sidebar-collapsed .nav-item:hover .tooltip {
            visibility: visible;
            opacity: 1;
        }
        .sidebar-collapsed .submenu {
            display: none;
        }
        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 30;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
        <!-- Mobile Overlay -->
        <div id="mobileOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>
        
        <!-- Sidebar -->
        <div id="sidebar" class="fixed md:relative z-50 bg-white shadow-lg sidebar-transition sidebar-expanded sidebar-mobile h-full">
            <div class="p-4 md:p-6">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i data-lucide="trending-up" class="w-5 h-5 text-white"></i>
                    </div>
                    <div class="sidebar-text overflow-hidden">
                        <h1 class="text-lg md:text-xl font-bold text-gray-800 whitespace-nowrap">Helmet</h1>
                        <p class="text-xs md:text-sm text-gray-500 whitespace-nowrap">ERP</p>
                    </div>
                </div>
            </div>
            
            <nav class="mt-6 md:mt-8">
                <div class="px-4 md:px-6 py-3 bg-indigo-50 border-r-4 border-indigo-500">
                    <a href="#" class="flex items-center space-x-3 text-indigo-600 nav-item relative" onclick="toggleSubmenu(event, 'dashboard-submenu')">
                        <i data-lucide="layout-dashboard" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="font-medium sidebar-text overflow-hidden whitespace-nowrap">Dashboard</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="dashboard-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Dashboard
                        </div>
                    </a>
                    <div class="submenu" id="dashboard-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="#" class="block py-2 text-sm text-indigo-500 hover:text-indigo-700">Analytics</a>
                            <a href="#" class="block py-2 text-sm text-indigo-500 hover:text-indigo-700">Reports</a>
                            <a href="#" class="block py-2 text-sm text-indigo-500 hover:text-indigo-700">Overview</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'project-submenu')">
                        <i data-lucide="folder-open" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Project Management</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="project-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Project Management
                        </div>
                    </a>
                    <div class="submenu" id="project-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="<?= base_url('admin/projects') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">All Projects</a>
                            <a href="<?= base_url('admin/projects/create') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">New Project</a>
                            <a href="<?= base_url('admin/project-categories') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Categories</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'tasks-submenu')">
                        <i data-lucide="check-square" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Tasks</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="tasks-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Tasks
                        </div>
                    </a>
                    <div class="submenu" id="tasks-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="<?= base_url('admin/tasks') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">All Tasks</a>
                            <a href="<?= base_url('admin/tasks/create') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">New Task</a>
                            <a href="<?= base_url('admin/tasks/calendar') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Calendar View</a>
                            <a href="<?= base_url('admin/tasks?status=pending') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Pending Tasks</a>
                            <a href="<?= base_url('admin/tasks?status=in_progress') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">In Progress</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'milestones-submenu')">
                        <i data-lucide="flag" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Milestones</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="milestones-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Milestones
                        </div>
                    </a>
                    <div class="submenu" id="milestones-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="<?= base_url('admin/milestones') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">All Milestones</a>
                            <a href="<?= base_url('admin/milestones/create') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">New Milestone</a>
                            <a href="<?= base_url('admin/milestones?status=upcoming') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Upcoming</a>
                            <a href="<?= base_url('admin/milestones?status=completed') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Completed</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'clients-submenu')">
                        <i data-lucide="users" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Clients</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="clients-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Clients
                        </div>
                    </a>
                    <div class="submenu" id="clients-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="<?= base_url('admin/clients') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">All Clients</a>
                            <a href="<?= base_url('admin/clients/create') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">New Client</a>
                            <a href="<?= base_url('admin/clients?status=active') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Active Clients</a>
                            <a href="<?= base_url('admin/clients?client_type=company') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Companies</a>
                            <a href="<?= base_url('admin/clients?client_type=individual') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Individuals</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'message-submenu')">
                        <i data-lucide="message-circle" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Message</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="message-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Message
                        </div>
                    </a>
                    <div class="submenu" id="message-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Inbox</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Sent</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Drafts</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'inventory-submenu')">
                        <i data-lucide="package" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Inventory</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="inventory-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Inventory
                        </div>
                    </a>
                    <div class="submenu" id="inventory-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="<?= base_url('admin/materials') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Materials</a>
                            <a href="<?= base_url('admin/material-categories') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Categories</a>
                            <a href="<?= base_url('admin/warehouses') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Warehouses</a>
                            <a href="<?= base_url('admin/suppliers') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Suppliers</a>
                            <a href="<?= base_url('admin/materials/barcode-scanner') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Barcode Scanner</a>
                            <a href="<?= base_url('admin/materials/low-stock-notifications') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Low Stock</a>
                            <a href="<?= base_url('admin/materials/report') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Reports</a>
                        </div>
                    </div>
                </div>
                
               
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'notification-submenu')">
                        <i data-lucide="bell" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Notification</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="notification-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Notification
                        </div>
                    </a>
                    <div class="submenu" id="notification-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Recent</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Unread</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Settings</a>
                        </div>
                    </div>
                </div>
                
                

                <!-- HR & Administration Section -->
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'hr-submenu')">
                        <i data-lucide="user-check" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">HR & Admin</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="hr-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            HR & Administration
                        </div>
                    </a>
                    <div class="submenu" id="hr-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="<?= base_url('admin/users') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Users</a>
                            <a href="<?= base_url('admin/roles') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Roles & Permissions</a>
                            <a href="<?= base_url('admin/departments') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Departments</a>
                            <a href="<?= base_url('admin/positions') ?>" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Job Positions</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative" onclick="toggleSubmenu(event, 'settings-submenu')">
                        <i data-lucide="settings" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Setting</span>
                        <i data-lucide="chevron-down" class="w-4 h-4 ml-auto sidebar-text menu-chevron" id="settings-chevron"></i>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Setting
                        </div>
                    </a>
                    <div class="submenu" id="settings-submenu">
                        <div class="sidebar-text ml-8 mt-2 space-y-1">
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">General</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Security</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Preferences</a>
                            <a href="#" class="block py-2 text-sm text-gray-500 hover:text-gray-700">Integrations</a>
                        </div>
                    </div>
                </div>
                
                <div class="px-4 md:px-6 py-3 hover:bg-gray-50 transition-colors">
                    <a href="#" class="flex items-center space-x-3 text-gray-600 nav-item relative">
                        <i data-lucide="credit-card" class="w-5 h-5 flex-shrink-0"></i>
                        <span class="sidebar-text overflow-hidden whitespace-nowrap">Plans</span>
                        <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                            Plans
                        </div>
                    </a>
                </div>
            </nav>
              <div class="absolute bottom-0 w-full p-4 md:p-6">
                <a href="<?= base_url('auth/logout') ?>" 
                   class="flex items-center space-x-3 text-gray-600 hover:text-gray-800 nav-item relative transition-colors duration-200"
                   onclick="return confirm('Are you sure you want to logout?')">
                    <i data-lucide="log-out" class="w-5 h-5 flex-shrink-0"></i>
                    <span class="sidebar-text overflow-hidden whitespace-nowrap">Log Out</span>
                    <div class="tooltip absolute left-16 bg-gray-800 text-white px-2 py-1 rounded text-sm whitespace-nowrap">
                        Log Out
                    </div>
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div id="mainContent" class="flex-1 overflow-auto">
            <!-- Sticky Header -->
            <div class="sticky-header bg-white border-b border-gray-200 px-4 md:px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <!-- Mobile Menu Button -->
                        <button id="mobileMenuBtn" class="p-2 hover:bg-gray-100 rounded-lg md:hidden">
                            <i data-lucide="menu" class="w-5 h-5 text-gray-600"></i>
                        </button>
                        
                        <!-- Desktop Sidebar Toggle -->
                        <button id="sidebarToggle" class="p-2 hover:bg-gray-100 rounded-lg hidden md:block">
                            <i data-lucide="menu" class="w-5 h-5 text-gray-600"></i>
                        </button>
                        
                        <h2 class="text-xl md:text-2xl font-bold text-gray-800"><?= $pageTitle ?? 'Dashboard' ?></h2>
                    </div>
                    
                    <div class="flex items-center space-x-2 md:space-x-4">
                        <div class="relative hidden md:block">
                            <input type="text" placeholder="Search" class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 w-48 lg:w-64">
                            <i data-lucide="search" class="w-4 h-4 absolute left-3 top-3 text-gray-400"></i>
                        </div>
                        
                        <!-- Mobile Search Button -->
                        <button class="p-2 hover:bg-gray-100 rounded-lg md:hidden">
                            <i data-lucide="search" class="w-5 h-5 text-gray-600"></i>
                        </button>
                        
                        <div class="hidden md:flex items-center space-x-2">
                            <span class="text-sm text-gray-600">ID</span>
                            <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400"></i>
                        </div>
                          <div class="relative">
                            <div class="flex items-center space-x-2 md:space-x-3 cursor-pointer" onclick="toggleUserDropdown()">
                                <div class="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center">
                                    <span class="text-white text-sm font-medium"><?= strtoupper(substr(session('first_name'), 0, 1)) ?></span>
                                </div>
                                <div class="text-sm hidden md:block">
                                    <div class="font-medium text-gray-800"><?= session('full_name') ?? 'User' ?></div>
                                    <div class="text-gray-500 text-xs"><?= session('email') ?? '<EMAIL>' ?></div>
                                </div>
                                <i data-lucide="chevron-down" class="w-4 h-4 text-gray-400 hidden md:block"></i>
                            </div>
                            
                            <!-- User Dropdown Menu -->
                            <div id="userDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden z-50">
                                <div class="py-1">
                                    <a href="<?= base_url('admin/users/profile') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="user" class="w-4 h-4 mr-2"></i>
                                        Profile
                                    </a>
                                    <a href="<?= base_url('admin/settings') ?>" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                                        Settings
                                    </a>
                                    <hr class="my-1">
                                    <a href="<?= base_url('auth/logout') ?>" 
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-red-600"
                                       onclick="return confirm('Are you sure you want to logout?')">
                                        <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
                                        Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dashboard Content -->
            <div class="p-4 md:p-6">
                <!-- Flash Messages -->
                <?php if (session('success')): ?>
                    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-lg relative" role="alert">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="check-circle" class="w-5 h-5 mr-2"></i>
                            </div>
                            <div>
                                <p class="font-medium"><?= session('success') ?></p>
                            </div>
                            <button type="button" class="ml-auto -mx-1.5 -my-1.5 text-green-500 p-1.5 hover:text-green-800 rounded-lg focus:ring-2 focus:ring-green-400" onclick="this.parentElement.parentElement.remove()">
                                <i data-lucide="x" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (session('error')): ?>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg relative" role="alert">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="alert-circle" class="w-5 h-5 mr-2"></i>
                            </div>
                            <div>
                                <p class="font-medium"><?= session('error') ?></p>
                            </div>
                            <button type="button" class="ml-auto -mx-1.5 -my-1.5 text-red-500 p-1.5 hover:text-red-800 rounded-lg focus:ring-2 focus:ring-red-400" onclick="this.parentElement.parentElement.remove()">
                                <i data-lucide="x" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (session('warning')): ?>
                    <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-6 rounded-lg relative" role="alert">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="alert-triangle" class="w-5 h-5 mr-2"></i>
                            </div>
                            <div>
                                <p class="font-medium"><?= session('warning') ?></p>
                            </div>
                            <button type="button" class="ml-auto -mx-1.5 -my-1.5 text-yellow-500 p-1.5 hover:text-yellow-800 rounded-lg focus:ring-2 focus:ring-yellow-400" onclick="this.parentElement.parentElement.remove()">
                                <i data-lucide="x" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Debug Section for Validation Errors -->
                <?php $validation = \Config\Services::validation(); ?>
                <?php if ($validation->getErrors() && ENVIRONMENT === 'development'): ?>
                    <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-6 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="info" class="w-5 h-5 mr-2"></i>
                            </div>
                            <div>
                                <p class="font-medium">Validation Debug Information:</p>
                                <pre class="mt-2 text-sm bg-blue-50 p-2 rounded overflow-auto max-h-64"><?= print_r($validation->getErrors(), true) ?></pre>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Validation Errors Summary (if any) -->
                <?php if ($validation->getErrors()): ?>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i data-lucide="alert-octagon" class="w-5 h-5 mr-2"></i>
                            </div>
                            <div>
                                <p class="font-medium">Please fix the following errors:</p>
                                <ul class="mt-1 ml-4 list-disc list-inside text-sm">
                                    <?php foreach($validation->getErrors() as $field => $error): ?>
                                        <li><strong><?= ucfirst(str_replace('_', ' ', $field)) ?>:</strong> <?= esc($error) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

        <?= $this->renderSection('content') ?>
            </div>
        </div>
    </div>

    <script>
        // Submenu toggle functionality
        function toggleSubmenu(event, submenuId) {
            event.preventDefault();
            
            // Don't toggle if sidebar is collapsed
            if (sidebar.classList.contains('sidebar-collapsed')) {
                return;
            }
            
            const submenu = document.getElementById(submenuId);
            const chevron = document.getElementById(submenuId.replace('-submenu', '-chevron'));
            
            // Close all other submenus
            document.querySelectorAll('.submenu').forEach(menu => {
                if (menu.id !== submenuId && menu.classList.contains('open')) {
                    menu.classList.remove('open');
                    const otherChevron = document.getElementById(menu.id.replace('-submenu', '-chevron'));
                    if (otherChevron) {
                        otherChevron.classList.remove('rotated');
                    }
                }
            });
            
            // Toggle current submenu
            submenu.classList.toggle('open');
            if (chevron) {
                chevron.classList.toggle('rotated');
            }
        }        // Initialize Lucide icons
        lucide.createIcons();
        
        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('hidden');
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const userProfile = event.target.closest('.relative');
            
            if (!userProfile && !dropdown.classList.contains('hidden')) {
                dropdown.classList.add('hidden');
            }
        });
        
        // Sidebar functionality
        const sidebar = document.getElementById('sidebar');
        const mobileOverlay = document.getElementById('mobileOverlay');
        const mobileMenuBtn = document.getElementById('mobileMenuBtn');
        const sidebarToggle = document.getElementById('sidebarToggle');
        
        let isCollapsed = false;
        let isMobileMenuOpen = false;
        
        // Mobile menu toggle
        mobileMenuBtn.addEventListener('click', () => {
            isMobileMenuOpen = !isMobileMenuOpen;
            
            if (isMobileMenuOpen) {
                sidebar.classList.add('open');
                mobileOverlay.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            } else {
                sidebar.classList.remove('open');
                mobileOverlay.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        });
        
        // Close mobile menu when clicking overlay
        mobileOverlay.addEventListener('click', () => {
            sidebar.classList.remove('open');
            mobileOverlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
            isMobileMenuOpen = false;
        });
        
        // Desktop sidebar toggle
        sidebarToggle.addEventListener('click', () => {
            isCollapsed = !isCollapsed;
            
            if (isCollapsed) {
                sidebar.classList.remove('sidebar-expanded');
                sidebar.classList.add('sidebar-collapsed');
            } else {
                sidebar.classList.remove('sidebar-collapsed');
                sidebar.classList.add('sidebar-expanded');
            }
        });
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 768) {
                sidebar.classList.remove('open');
                mobileOverlay.classList.add('hidden');
                document.body.style.overflow = 'auto';
                isMobileMenuOpen = false;
            }
        });

        // Close mobile menu when clicking on nav items
        const navItems = sidebar.querySelectorAll('nav a');
        navItems.forEach(item => {
            item.addEventListener('click', () => {
                if (window.innerWidth < 768) {
                    sidebar.classList.remove('open');
                    mobileOverlay.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                    isMobileMenuOpen = false;
                }
            });
        });

        // Client Statistics Chart
        const clientCtx = document.getElementById('clientChart').getContext('2d');
        new Chart(clientCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
                datasets: [{
                    label: 'US',
                    data: [30, 35, 40, 45, 55, 50, 60],
                    borderColor: '#6366f1',
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'UK',
                    data: [25, 30, 35, 40, 50, 45, 55],
                    borderColor: '#ef4444',
                    backgroundColor: 'rgba(239, 68, 68, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            display: false
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Site Health Chart
        const siteCtx = document.getElementById('siteHealthChart').getContext('2d');
        new Chart(siteCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [84, 16],
                    backgroundColor: ['#6366f1', '#e5e7eb'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // Online Sales Chart
        const salesCtx = document.getElementById('onlineSalesChart').getContext('2d');
        new Chart(salesCtx, {
            type: 'doughnut',
            data: {
                datasets: [{
                    data: [80, 20],
                    backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    </script>
    <?= $this->renderSection('js') ?>
    <?= $this->renderSection('scripts') ?>
</body>
</html>