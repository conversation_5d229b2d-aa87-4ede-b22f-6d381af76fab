<?= $this->extend('layouts/admin') ?>

<?= $this->section('content') ?>
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800"><?= esc($title) ?></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?= base_url('admin/materials') ?>">Materials</a></li>
                    <li class="breadcrumb-item active">Stock Movement</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="<?= base_url('admin/materials') ?>" class="btn btn-secondary">
                <i data-lucide="arrow-left" class="w-4 h-4 me-2"></i>Back to Materials
            </a>
        </div>
    </div>

    <!-- Material Info Card -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 class="card-title"><?= esc($material['name']) ?></h5>
                    <p class="text-muted mb-1">Item Code: <strong><?= esc($material['item_code']) ?></strong></p>
                    <p class="text-muted mb-1">Current Stock: <strong><?= number_format($material['current_stock'] ?? 0, 2) ?> <?= esc($material['unit']) ?></strong></p>
                    <p class="text-muted mb-0">Unit Cost: <strong>$<?= number_format($material['unit_cost'] ?? 0, 2) ?></strong></p>
                </div>
                <div class="col-md-6 text-md-end">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#recordMovementModal">
                        <i data-lucide="plus" class="w-4 h-4 me-2"></i>Record Movement
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Movement History -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Stock Movement History</h5>
        </div>
        <div class="card-body">
            <?php if (empty($movements)): ?>
                <div class="text-center py-4">
                    <i data-lucide="package" class="w-12 h-12 text-muted mb-3"></i>
                    <p class="text-muted">No stock movements recorded for this material yet.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Reference</th>
                                <th>Type</th>
                                <th>Quantity</th>
                                <th>Source</th>
                                <th>Destination</th>
                                <th>Project</th>
                                <th>Task</th>
                                <th>Performed By</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($movements as $movement): ?>
                                <tr>
                                    <td>
                                        <small class="text-muted">
                                            <?= date('M d, Y', strtotime($movement['created_at'])) ?><br>
                                            <?= date('H:i', strtotime($movement['created_at'])) ?>
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= esc($movement['reference_number']) ?></span>
                                    </td>
                                    <td>
                                        <?php
                                        $typeClass = '';
                                        switch($movement['movement_type']) {
                                            case 'in':
                                            case 'purchase':
                                                $typeClass = 'bg-success';
                                                break;
                                            case 'out':
                                            case 'project_usage':
                                                $typeClass = 'bg-warning';
                                                break;
                                            case 'transfer':
                                                $typeClass = 'bg-info';
                                                break;
                                            case 'adjustment':
                                                $typeClass = 'bg-primary';
                                                break;
                                            default:
                                                $typeClass = 'bg-secondary';
                                        }
                                        ?>
                                        <span class="badge <?= $typeClass ?>"><?= ucfirst(str_replace('_', ' ', $movement['movement_type'])) ?></span>
                                    </td>
                                    <td>
                                        <strong><?= number_format($movement['quantity'], 2) ?></strong> <?= esc($movement['unit']) ?>
                                        <?php if ($movement['unit_cost'] > 0): ?>
                                            <br><small class="text-muted">@ $<?= number_format($movement['unit_cost'], 2) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $movement['source_warehouse_name'] ? esc($movement['source_warehouse_name']) : '-' ?>
                                    </td>
                                    <td>
                                        <?= $movement['destination_warehouse_name'] ? esc($movement['destination_warehouse_name']) : '-' ?>
                                    </td>
                                    <td>
                                        <?= $movement['project_name'] ? esc($movement['project_name']) : '-' ?>
                                    </td>
                                    <td>
                                        <?= $movement['task_name'] ? esc($movement['task_name']) : '-' ?>
                                    </td>
                                    <td>
                                        <?php if ($movement['performer_first_name']): ?>
                                            <?= esc($movement['performer_first_name'] . ' ' . $movement['performer_last_name']) ?>
                                        <?php else: ?>
                                            <span class="text-muted">System</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?= $movement['notes'] ? esc($movement['notes']) : '-' ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Record Movement Modal -->
<div class="modal fade" id="recordMovementModal" tabindex="-1" aria-labelledby="recordMovementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="recordMovementModalLabel">Record Stock Movement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('admin/materials/record-stock-movement/' . $material['id']) ?>" method="POST">
                <?= csrf_field() ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="movement_type" class="form-label">Movement Type <span class="text-danger">*</span></label>
                                <select name="movement_type" id="movement_type" class="form-select" required>
                                    <option value="">Select Type</option>
                                    <option value="in">Stock In</option>
                                    <option value="out">Stock Out</option>
                                    <option value="transfer">Transfer</option>
                                    <option value="adjustment">Adjustment</option>
                                    <option value="project_usage">Project Usage</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                                <input type="number" name="quantity" id="quantity" class="form-control" step="0.01" min="0.01" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit_cost" class="form-label">Unit Cost</label>
                                <input type="number" name="unit_cost" id="unit_cost" class="form-control" step="0.01" min="0" value="<?= $material['unit_cost'] ?? 0 ?>">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="source_warehouse_id" class="form-label">Source Warehouse</label>
                                <select name="source_warehouse_id" id="source_warehouse_id" class="form-select">
                                    <option value="">Select Warehouse</option>
                                    <?php foreach ($warehouses as $warehouse): ?>
                                        <option value="<?= $warehouse['id'] ?>"><?= esc($warehouse['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row" id="destination_warehouse_row" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="destination_warehouse_id" class="form-label">Destination Warehouse</label>
                                <select name="destination_warehouse_id" id="destination_warehouse_id" class="form-select">
                                    <option value="">Select Warehouse</option>
                                    <?php foreach ($warehouses as $warehouse): ?>
                                        <option value="<?= $warehouse['id'] ?>"><?= esc($warehouse['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Record Movement</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Lucide icons
    lucide.createIcons();
    
    // Show/hide destination warehouse based on movement type
    const movementTypeSelect = document.getElementById('movement_type');
    const destinationWarehouseRow = document.getElementById('destination_warehouse_row');
    
    movementTypeSelect.addEventListener('change', function() {
        if (this.value === 'transfer') {
            destinationWarehouseRow.style.display = 'block';
        } else {
            destinationWarehouseRow.style.display = 'none';
        }
    });
});
</script>

<?= $this->endSection() ?>
